import { addons } from '@storybook/manager-api';
import { create } from '@storybook/theming/create';

const theme = create({
  base: 'light',
  brandTitle: 'Cohere UI Library',
  brandUrl: 'https://cohere.ai',
  brandTarget: '_self',
  
  colorPrimary: '#FF2F00',
  colorSecondary: '#00B958',
  
  // UI
  appBg: '#FAFAFA',
  appContentBg: '#FFFFFF',
  appBorderColor: '#E7DFD2',
  appBorderRadius: 8,
  
  // Typography
  fontBase: '"ui-sans-serif", "system-ui", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", "Noto Sans", sans-serif',
  fontCode: 'monospace',
  
  // Text colors
  textColor: '#39394A',
  textInverseColor: '#FFFFFF',
  
  // Toolbar default and active colors
  barTextColor: '#39394A',
  barSelectedColor: '#FF2F00',
  barBg: '#FAFAFA',
  
  // Form colors
  inputBg: '#FFFFFF',
  inputBorder: '#E7DFD2',
  inputTextColor: '#39394A',
  inputBorderRadius: 8,
});

addons.setConfig({
  theme,
});
