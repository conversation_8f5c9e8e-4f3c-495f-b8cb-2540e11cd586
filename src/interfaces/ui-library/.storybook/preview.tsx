import type { Preview } from '@storybook/react';
import React from 'react';

import '../src/styles/globals.css';

const preview: Preview = {
  decorators: [
    (Story) => (
      <div className="grid h-screen w-screen grid-cols-2">
        <main className="dark bg-volcanic-100 p-6">
          <div className="text-white">
            <h3 className="mb-4 text-sm font-medium text-volcanic-400">Dark Theme</h3>
            <Story />
          </div>
        </main>
        <main className="bg-marble-950 p-6">
          <div className="text-volcanic-300">
            <h3 className="mb-4 text-sm font-medium text-volcanic-600">Light Theme</h3>
            <Story />
          </div>
        </main>
      </div>
    ),
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    layout: 'fullscreen',
    backgrounds: {
      disable: true,
    },
    docs: {
      theme: {
        base: 'light',
        colorPrimary: '#FF2F00',
        colorSecondary: '#00B958',
        appBg: '#FAFAFA',
        appContentBg: '#FFFFFF',
        appBorderColor: '#E7DFD2',
        textColor: '#39394A',
        textInverseColor: '#FFFFFF',
        barTextColor: '#39394A',
        barSelectedColor: '#FF2F00',
        barBg: '#FAFAFA',
        inputBg: '#FFFFFF',
        inputBorder: '#E7DFD2',
        inputTextColor: '#39394A',
        inputBorderRadius: 8,
      },
    },
  },
  globalTypes: {
    theme: {
      description: 'Global theme for components',
      defaultValue: 'light',
      toolbar: {
        title: 'Theme',
        icon: 'circlehollow',
        items: [
          { value: 'light', icon: 'circlehollow', title: 'Light' },
          { value: 'dark', icon: 'circle', title: 'Dark' },
          { value: 'side-by-side', icon: 'sidebar', title: 'Side by side' },
        ],
        showName: true,
        dynamicTitle: true,
      },
    },
  },
};

export default preview;
