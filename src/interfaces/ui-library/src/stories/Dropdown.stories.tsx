import type { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { Dropdown } from '../components/ui'

const meta: Meta<typeof Dropdown> = {
  title: 'UI/Dropdown',
  component: Dropdown,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
  },
}

export default meta
type Story = StoryObj<typeof Dropdown>

const basicOptions = [
  { value: 'apple', label: 'Apple' },
  { value: 'banana', label: 'Banana' },
  { value: 'cherry', label: 'Cherry' },
  { value: 'date', label: 'Date' },
  { value: 'elderberry', label: 'Elderberry' },
]

const countryOptions = [
  { value: 'us', label: 'United States' },
  { value: 'ca', label: 'Canada' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'de', label: 'Germany' },
  { value: 'fr', label: 'France' },
  { value: 'jp', label: 'Japan' },
  { value: 'au', label: 'Australia' },
]

export const Default: Story = {
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <Dropdown
          options={basicOptions}
          value={value}
          onChange={setValue}
          placeholder="Select a fruit"
        />
      </div>
    )
  },
}

export const WithLabel: Story = {
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <Dropdown
          label="Favorite Fruit"
          options={basicOptions}
          value={value}
          onChange={setValue}
          placeholder="Choose your favorite"
        />
      </div>
    )
  },
}

export const Required: Story = {
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <Dropdown
          label="Country"
          required
          options={countryOptions}
          value={value}
          onChange={setValue}
          placeholder="Select your country"
        />
      </div>
    )
  },
}

export const WithError: Story = {
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <Dropdown
          label="Country"
          required
          options={countryOptions}
          value={value}
          onChange={setValue}
          placeholder="Select your country"
          error="Please select a country"
        />
      </div>
    )
  },
}

export const Small: Story = {
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-64">
        <Dropdown
          label="Size"
          size="sm"
          options={[
            { value: 'xs', label: 'Extra Small' },
            { value: 's', label: 'Small' },
            { value: 'm', label: 'Medium' },
            { value: 'l', label: 'Large' },
            { value: 'xl', label: 'Extra Large' },
          ]}
          value={value}
          onChange={setValue}
          placeholder="Select size"
        />
      </div>
    )
  },
}

export const Large: Story = {
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-96">
        <Dropdown
          label="Department"
          size="lg"
          options={[
            { value: 'engineering', label: 'Engineering' },
            { value: 'design', label: 'Design' },
            { value: 'product', label: 'Product Management' },
            { value: 'marketing', label: 'Marketing' },
            { value: 'sales', label: 'Sales' },
          ]}
          value={value}
          onChange={setValue}
          placeholder="Select department"
        />
      </div>
    )
  },
}

export const Disabled: Story = {
  render: () => {
    return (
      <div className="w-80">
        <Dropdown
          label="Disabled Dropdown"
          options={basicOptions}
          value="apple"
          onChange={() => {}}
          disabled
        />
      </div>
    )
  },
}

export const WithDisabledOptions: Story = {
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <Dropdown
          label="Subscription Plan"
          options={[
            { value: 'free', label: 'Free' },
            { value: 'basic', label: 'Basic - $9/month' },
            { value: 'pro', label: 'Pro - $29/month' },
            { value: 'enterprise', label: 'Enterprise - Contact Sales', disabled: true },
          ]}
          value={value}
          onChange={setValue}
          placeholder="Select a plan"
        />
      </div>
    )
  },
}

export const LongOptions: Story = {
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <Dropdown
          label="Programming Language"
          options={[
            { value: 'js', label: 'JavaScript' },
            { value: 'ts', label: 'TypeScript' },
            { value: 'py', label: 'Python' },
            { value: 'java', label: 'Java' },
            { value: 'cpp', label: 'C++' },
            { value: 'cs', label: 'C#' },
            { value: 'go', label: 'Go' },
            { value: 'rust', label: 'Rust' },
            { value: 'swift', label: 'Swift' },
            { value: 'kotlin', label: 'Kotlin' },
            { value: 'php', label: 'PHP' },
            { value: 'ruby', label: 'Ruby' },
            { value: 'scala', label: 'Scala' },
            { value: 'dart', label: 'Dart' },
            { value: 'r', label: 'R' },
          ]}
          value={value}
          onChange={setValue}
          placeholder="Select a language"
        />
      </div>
    )
  },
}

export const FormExample: Story = {
  render: () => {
    const [country, setCountry] = useState<string>('')
    const [fruit, setFruit] = useState<string>('')
    const [size, setSize] = useState<string>('')
    
    return (
      <div className="w-80 space-y-6">
        <h3 className="text-lg font-semibold">User Preferences</h3>
        
        <Dropdown
          label="Country"
          required
          options={countryOptions}
          value={country}
          onChange={setCountry}
          placeholder="Select your country"
        />
        
        <Dropdown
          label="Favorite Fruit"
          options={basicOptions}
          value={fruit}
          onChange={setFruit}
          placeholder="Choose your favorite"
        />
        
        <Dropdown
          label="T-Shirt Size"
          size="sm"
          options={[
            { value: 'xs', label: 'XS' },
            { value: 's', label: 'S' },
            { value: 'm', label: 'M' },
            { value: 'l', label: 'L' },
            { value: 'xl', label: 'XL' },
          ]}
          value={size}
          onChange={setSize}
          placeholder="Select size"
        />
        
        <div className="pt-4">
          <h4 className="font-medium mb-2">Selected Values:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>Country: {country || 'None'}</li>
            <li>Fruit: {fruit || 'None'}</li>
            <li>Size: {size || 'None'}</li>
          </ul>
        </div>
      </div>
    )
  },
}
