import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { KebabMenu } from '../components/ui'
import { 
  Copy, 
  Edit, 
  MoreHorizontal, 
  Share, 
  Trash2, 
  Download,
  Eye,
  Star,
  Archive,
} from 'lucide-react'

const meta: Meta<typeof KebabMenu> = {
  title: 'UI/KebabMenu',
  component: KebabMenu,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
    variant: {
      control: 'select',
      options: ['default', 'ghost'],
    },
    side: {
      control: 'select',
      options: ['top', 'right', 'bottom', 'left'],
    },
    align: {
      control: 'select',
      options: ['start', 'center', 'end'],
    },
  },
}

export default meta
type Story = StoryObj<typeof KebabMenu>

export const Default: Story = {
  args: {
    items: [
      {
        label: 'Edit',
        icon: <Edit className="h-4 w-4" />,
        onClick: () => alert('Edit clicked'),
      },
      {
        label: 'Copy',
        icon: <Copy className="h-4 w-4" />,
        onClick: () => alert('Copy clicked'),
      },
      {
        label: 'Share',
        icon: <Share className="h-4 w-4" />,
        onClick: () => alert('Share clicked'),
      },
      {
        label: 'Delete',
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
        onClick: () => alert('Delete clicked'),
        separator: true,
      },
    ],
  },
}

export const Small: Story = {
  args: {
    size: 'sm',
    items: [
      {
        label: 'View',
        icon: <Eye className="h-3 w-3" />,
        onClick: () => alert('View clicked'),
      },
      {
        label: 'Edit',
        icon: <Edit className="h-3 w-3" />,
        onClick: () => alert('Edit clicked'),
      },
      {
        label: 'Delete',
        icon: <Trash2 className="h-3 w-3" />,
        variant: 'destructive',
        onClick: () => alert('Delete clicked'),
      },
    ],
  },
}

export const Large: Story = {
  args: {
    size: 'lg',
    items: [
      {
        label: 'Download',
        icon: <Download className="h-5 w-5" />,
        onClick: () => alert('Download clicked'),
      },
      {
        label: 'Add to favorites',
        icon: <Star className="h-5 w-5" />,
        onClick: () => alert('Favorite clicked'),
      },
      {
        label: 'Archive',
        icon: <Archive className="h-5 w-5" />,
        onClick: () => alert('Archive clicked'),
      },
      {
        label: 'Delete permanently',
        icon: <Trash2 className="h-5 w-5" />,
        variant: 'destructive',
        onClick: () => alert('Delete clicked'),
        separator: true,
      },
    ],
  },
}

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    items: [
      {
        label: 'Edit',
        icon: <Edit className="h-4 w-4" />,
        onClick: () => alert('Edit clicked'),
      },
      {
        label: 'Copy',
        icon: <Copy className="h-4 w-4" />,
        onClick: () => alert('Copy clicked'),
      },
      {
        label: 'Delete',
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
        onClick: () => alert('Delete clicked'),
      },
    ],
  },
}

export const CustomIcon: Story = {
  args: {
    icon: <MoreHorizontal className="h-4 w-4" />,
    items: [
      {
        label: 'Edit',
        icon: <Edit className="h-4 w-4" />,
        onClick: () => alert('Edit clicked'),
      },
      {
        label: 'Copy',
        icon: <Copy className="h-4 w-4" />,
        onClick: () => alert('Copy clicked'),
      },
      {
        label: 'Delete',
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
        onClick: () => alert('Delete clicked'),
      },
    ],
  },
}

export const WithDisabledItems: Story = {
  args: {
    items: [
      {
        label: 'Edit',
        icon: <Edit className="h-4 w-4" />,
        onClick: () => alert('Edit clicked'),
      },
      {
        label: 'Copy',
        icon: <Copy className="h-4 w-4" />,
        disabled: true,
        onClick: () => alert('Copy clicked'),
      },
      {
        label: 'Share',
        icon: <Share className="h-4 w-4" />,
        onClick: () => alert('Share clicked'),
      },
      {
        label: 'Delete',
        icon: <Trash2 className="h-4 w-4" />,
        variant: 'destructive',
        disabled: true,
        onClick: () => alert('Delete clicked'),
      },
    ],
  },
}

export const WithHiddenItems: Story = {
  render: () => {
    const [showAdvanced, setShowAdvanced] = useState(false)
    
    return (
      <div className="space-y-4">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={showAdvanced}
            onChange={(e) => setShowAdvanced(e.target.checked)}
          />
          <span>Show advanced options</span>
        </label>
        
        <KebabMenu
          items={[
            {
              label: 'Edit',
              icon: <Edit className="h-4 w-4" />,
              onClick: () => alert('Edit clicked'),
            },
            {
              label: 'Copy',
              icon: <Copy className="h-4 w-4" />,
              onClick: () => alert('Copy clicked'),
            },
            {
              label: 'Advanced Settings',
              icon: <Star className="h-4 w-4" />,
              visible: showAdvanced,
              onClick: () => alert('Advanced clicked'),
            },
            {
              label: 'Delete',
              icon: <Trash2 className="h-4 w-4" />,
              variant: 'destructive',
              onClick: () => alert('Delete clicked'),
              separator: true,
            },
          ]}
        />
      </div>
    )
  },
}

export const Positioning: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-8 p-8">
      <div className="space-y-4">
        <h3 className="font-semibold">Side Positioning</h3>
        <div className="flex gap-4">
          <KebabMenu
            side="top"
            items={[
              { label: 'Top', onClick: () => alert('Top') },
              { label: 'Menu', onClick: () => alert('Menu') },
            ]}
          />
          <KebabMenu
            side="bottom"
            items={[
              { label: 'Bottom', onClick: () => alert('Bottom') },
              { label: 'Menu', onClick: () => alert('Menu') },
            ]}
          />
          <KebabMenu
            side="left"
            items={[
              { label: 'Left', onClick: () => alert('Left') },
              { label: 'Menu', onClick: () => alert('Menu') },
            ]}
          />
          <KebabMenu
            side="right"
            items={[
              { label: 'Right', onClick: () => alert('Right') },
              { label: 'Menu', onClick: () => alert('Menu') },
            ]}
          />
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="font-semibold">Align Positioning</h3>
        <div className="flex gap-4">
          <KebabMenu
            align="start"
            items={[
              { label: 'Start', onClick: () => alert('Start') },
              { label: 'Aligned', onClick: () => alert('Aligned') },
            ]}
          />
          <KebabMenu
            align="center"
            items={[
              { label: 'Center', onClick: () => alert('Center') },
              { label: 'Aligned', onClick: () => alert('Aligned') },
            ]}
          />
          <KebabMenu
            align="end"
            items={[
              { label: 'End', onClick: () => alert('End') },
              { label: 'Aligned', onClick: () => alert('Aligned') },
            ]}
          />
        </div>
      </div>
    </div>
  ),
}
