import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { useState } from 'react'
import { LongPressMenu, LongPressMenuItem, Button } from '../components/ui'
import { 
  Copy, 
  Edit, 
  Share, 
  Trash2, 
  Download,
  Star,
  Archive,
  Eye,
  Heart,
  MessageCircle,
  Bookmark,
} from 'lucide-react'

const meta: Meta<typeof LongPressMenu> = {
  title: 'UI/LongPressMenu',
  component: LongPressMenu,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
  },
}

export default meta
type Story = StoryObj<typeof LongPressMenu>

export const Default: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <>
        <Button onClick={() => setIsOpen(true)}>
          Open Long Press Menu
        </Button>
        
        <LongPressMenu
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          items={[
            {
              label: 'Edit',
              icon: <Edit className="h-5 w-5" />,
              onClick: () => alert('Edit clicked'),
            },
            {
              label: 'Copy',
              icon: <Copy className="h-5 w-5" />,
              onClick: () => alert('Copy clicked'),
            },
            {
              label: 'Share',
              icon: <Share className="h-5 w-5" />,
              onClick: () => alert('Share clicked'),
            },
            {
              label: 'Delete',
              icon: <Trash2 className="h-5 w-5" />,
              variant: 'destructive',
              onClick: () => alert('Delete clicked'),
            },
          ]}
        />
      </>
    )
  },
}

export const WithTitle: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <>
        <Button onClick={() => setIsOpen(true)}>
          Open Menu with Title
        </Button>
        
        <LongPressMenu
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Choose an action"
          items={[
            {
              label: 'View Details',
              icon: <Eye className="h-5 w-5" />,
              onClick: () => alert('View clicked'),
            },
            {
              label: 'Add to Favorites',
              icon: <Star className="h-5 w-5" />,
              onClick: () => alert('Favorite clicked'),
            },
            {
              label: 'Download',
              icon: <Download className="h-5 w-5" />,
              onClick: () => alert('Download clicked'),
            },
            {
              label: 'Archive',
              icon: <Archive className="h-5 w-5" />,
              onClick: () => alert('Archive clicked'),
            },
          ]}
        />
      </>
    )
  },
}

export const Small: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <>
        <Button size="sm" onClick={() => setIsOpen(true)}>
          Small Menu
        </Button>
        
        <LongPressMenu
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          size="sm"
          items={[
            {
              label: 'Like',
              icon: <Heart className="h-4 w-4" />,
              onClick: () => alert('Like clicked'),
            },
            {
              label: 'Comment',
              icon: <MessageCircle className="h-4 w-4" />,
              onClick: () => alert('Comment clicked'),
            },
            {
              label: 'Share',
              icon: <Share className="h-4 w-4" />,
              onClick: () => alert('Share clicked'),
            },
          ]}
        />
      </>
    )
  },
}

export const Large: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <>
        <Button size="lg" onClick={() => setIsOpen(true)}>
          Large Menu
        </Button>
        
        <LongPressMenu
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          size="lg"
          title="Document Actions"
          items={[
            {
              label: 'Open in New Tab',
              icon: <Eye className="h-6 w-6" />,
              onClick: () => alert('Open clicked'),
            },
            {
              label: 'Download PDF',
              icon: <Download className="h-6 w-6" />,
              onClick: () => alert('Download clicked'),
            },
            {
              label: 'Add Bookmark',
              icon: <Bookmark className="h-6 w-6" />,
              onClick: () => alert('Bookmark clicked'),
            },
            {
              label: 'Delete Document',
              icon: <Trash2 className="h-6 w-6" />,
              variant: 'destructive',
              onClick: () => alert('Delete clicked'),
            },
          ]}
        />
      </>
    )
  },
}

export const WithDisabledItems: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <>
        <Button onClick={() => setIsOpen(true)}>
          Menu with Disabled Items
        </Button>
        
        <LongPressMenu
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          items={[
            {
              label: 'Edit',
              icon: <Edit className="h-5 w-5" />,
              onClick: () => alert('Edit clicked'),
            },
            {
              label: 'Copy (Premium)',
              icon: <Copy className="h-5 w-5" />,
              disabled: true,
              onClick: () => alert('Copy clicked'),
            },
            {
              label: 'Share',
              icon: <Share className="h-5 w-5" />,
              onClick: () => alert('Share clicked'),
            },
            {
              label: 'Delete (Admin Only)',
              icon: <Trash2 className="h-5 w-5" />,
              variant: 'destructive',
              disabled: true,
              onClick: () => alert('Delete clicked'),
            },
          ]}
        />
      </>
    )
  },
}

export const CustomContent: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <>
        <Button onClick={() => setIsOpen(true)}>
          Custom Content Menu
        </Button>
        
        <LongPressMenu
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title="Share Options"
        >
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground text-center">
              Choose how you'd like to share this content
            </div>
            
            <LongPressMenuItem
              icon={<Copy className="h-5 w-5" />}
              onClick={() => {
                navigator.clipboard.writeText('https://example.com')
                alert('Link copied!')
              }}
            >
              Copy Link
            </LongPressMenuItem>
            
            <LongPressMenuItem
              icon={<MessageCircle className="h-5 w-5" />}
              onClick={() => alert('Share via message')}
            >
              Send Message
            </LongPressMenuItem>
            
            <LongPressMenuItem
              icon={<Download className="h-5 w-5" />}
              onClick={() => alert('Download started')}
            >
              Download for Offline
            </LongPressMenuItem>
            
            <div className="border-t pt-3">
              <LongPressMenuItem
                variant="destructive"
                icon={<Trash2 className="h-5 w-5" />}
                onClick={() => alert('Report submitted')}
              >
                Report Content
              </LongPressMenuItem>
            </div>
          </div>
        </LongPressMenu>
      </>
    )
  },
}

export const MobileExample: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false)
    
    return (
      <div className="max-w-sm mx-auto">
        <div className="bg-card border rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Photo.jpg</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(true)}
              onTouchStart={() => {
                // Simulate long press on mobile
                setTimeout(() => setIsOpen(true), 500)
              }}
            >
              ⋯
            </Button>
          </div>
          
          <div className="aspect-video bg-muted rounded flex items-center justify-center">
            <span className="text-muted-foreground">Image Preview</span>
          </div>
          
          <p className="text-sm text-muted-foreground">
            Tap and hold the menu button to see options
          </p>
        </div>
        
        <LongPressMenu
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          items={[
            {
              label: 'View Full Size',
              icon: <Eye className="h-5 w-5" />,
              onClick: () => alert('View full size'),
            },
            {
              label: 'Add to Album',
              icon: <Star className="h-5 w-5" />,
              onClick: () => alert('Added to album'),
            },
            {
              label: 'Share Photo',
              icon: <Share className="h-5 w-5" />,
              onClick: () => alert('Share photo'),
            },
            {
              label: 'Download',
              icon: <Download className="h-5 w-5" />,
              onClick: () => alert('Download started'),
            },
            {
              label: 'Delete Photo',
              icon: <Trash2 className="h-5 w-5" />,
              variant: 'destructive',
              onClick: () => alert('Photo deleted'),
            },
          ]}
        />
      </div>
    )
  },
}
