/**
 * Spacing and sizing tokens for Cohere Toolkit
 * Consolidated from both assistants_web and coral_web frontends
 */

export const spacing = {
  // Icon sizes
  'icon-xs': '12px',
  'icon-sm': '14px',
  'icon-md': '16px',
  'icon-lg': '24px',
  'icon-xl': '36px',
  
  // Cell heights (from coral_web)
  'cell-xs': '24px',
  'cell-sm': '32px',
  'cell-md': '40px',
  'cell-lg': '50px',
  'cell-xl': '64px',
  
  // Button height
  'cell-button': '40px',
} as const;

export const width = {
  // Icon widths
  'icon-xs': '12px',
  'icon-sm': '14px',
  'icon-md': '16px',
  'icon-lg': '24px',
  'icon-xl': '36px',
  
  // Button widths (from coral_web)
  'btn-sm': '280px',
  'btn-md': '312px',
  'btn-lg': '350px',
  'btn-xl': '370px',
  
  // Modal and dialog widths
  'modal': '648px',
  'toast': '320px',
  'toast-sm': '280px',
  
  // Citation and file widths (from coral_web)
  'citation-md': '250px',
  'citation-lg': '298px',
  'citation-xl': '320px',
  'file': '224px',
  
  // Panel widths (from coral_web)
  'edit-agent-panel': '350px',
  'edit-agent-panel-lg': '683px',
  'edit-agent-panel-2xl': '800px',
} as const;

export const minWidth = {
  // Menu and navigation
  'menu': '174px',
  
  // Panel widths (from assistants_web)
  'left-panel-collapsed': '82px',
  'left-panel-expanded': '288px',
  
  // Citation panels (from coral_web)
  'citation-panel-md': '259px',
  'citation-panel-lg': '325px',
  'citation-panel-xl': '349px',
  
  // Agents panels (from coral_web)
  'agents-panel-collapsed': '82px',
  'agents-panel-expanded': '288px',
  'agents-panel-expanded-lg': '320px',
  'left-panel-lg': '242px',
  'left-panel-2xl': '300px',
  'left-panel-3xl': '360px',
} as const;

export const maxWidth = {
  // Content widths
  'message': '976px',
  'share-content': '700px',
  
  // Panel constraints (from assistants_web)
  'left-panel-collapsed': '82px',
  'left-panel-expanded': '288px',
} as const;

export const maxHeight = {
  // Cell constraints (from coral_web)
  'cell-xs': '24px',
  'cell-sm': '32px',
  'cell-md': '40px',
  'cell-lg': '50px',
  'cell-xl': '64px',
} as const;

export const borderRadius = {
  none: '0px',
  sm: 'calc(var(--radius) - 4px)',
  md: 'calc(var(--radius) - 2px)',
  lg: 'var(--radius)',
  xl: '12px',
  '2xl': '16px',
  '3xl': '24px',
  full: '9999px',
} as const;

export const zIndex = {
  // Layer hierarchy (from coral_web)
  'main-section': '10',
  'tag-suggestions': '10',
  'drag-drop-input-overlay': '10',
  'configuration-drawer': '20',
  'selected-citation': '20',
  'backdrop': '20',
  'navigation': '30',
  'read-only-conversation-footer': '30',
  'guide-tooltip': '30',
  'modal': '50',
  'tooltip': '50',
  'dropdown': '60',
  'toast': '70',
  'menu': '90',
} as const;

export const boxShadow = {
  // Custom shadows (from coral_web)
  'drawer': '-10px 4px 12px -10px rgba(197, 188, 172, 0.48)',
  'menu': '0px 4px 12px 0px rgba(197, 188, 172, 0.48)',
  'top': '4px 0px 12px 0px rgba(197, 188, 172, 0.48)',
} as const;

export type Spacing = keyof typeof spacing;
export type Width = keyof typeof width;
export type MinWidth = keyof typeof minWidth;
export type MaxWidth = keyof typeof maxWidth;
