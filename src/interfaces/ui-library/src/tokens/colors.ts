/**
 * Unified color palette for Cohere Toolkit
 * Consolidated from both assistants_web and coral_web frontends
 */

export const colors = {
  // Base colors
  black: '#212121',
  white: '#FAFAFA',
  
  // Simulated Coral - Primary brand color
  coral: {
    950: '#FFEAE5',
    900: '#FFD5CC',
    800: '#FFAC99',
    700: '#FF8266',
    600: '#FF5833',
    500: '#FF2F00', // Primary coral
    400: '#CC2500',
    300: '#991C00',
    200: '#661300',
    150: '#330900',
  },
  
  // Mushroom Grey - Secondary neutral
  mushroom: {
    950: '#F4F3F0',
    900: '#E9E7E2',
    800: '#D2CDC4',
    700: '#BDB6A8',
    600: '#A79E8B',
    500: '#91856E',
    400: '#70695C',
    300: '#575042',
    200: '#3A352C',
    150: '#2C2821',
  },
  
  // Evolved Mushroom Grey - Accent variant
  'evolved-mushroom': {
    500: '#FFAA00',
    600: '#FFBB33',
    800: '#FFDC97',
  },
  
  // Marble White - Light backgrounds
  marble: {
    1000: '#FFFFFF',
    980: '#F9F9FB',
    950: '#EFEFF5',
    900: '#DFDFEC',
    850: '#D0D0E2',
    800: '#C4C4C4',
  },
  
  // Volcanic Black - Dark backgrounds and text
  volcanic: {
    950: '#F7F7F8',
    900: '#EDEDF0',
    800: '#DCDCE3',
    700: '#C5C5D2',
    600: '#9E9EAF',
    500: '#7C7C8A',
    400: '#62626C',
    300: '#39394A',
    200: '#2C2C3A',
    150: '#1F1F2E',
    100: '#13131F',
    60: '#0D0D15',
  },
  
  // Blue - Information and links
  blue: {
    950: '#EBF3FF',
    900: '#D6E7FF',
    800: '#ADCFFF',
    700: '#85B8FF',
    600: '#5CA0FF',
    500: '#3388FF',
    400: '#0F70FF',
    300: '#0066F5',
    200: '#0052CC',
    150: '#003D99',
  },
  
  // Evolved Blue - Accent variant
  'evolved-blue': {
    500: '#1B6AFF',
    600: '#4785FF',
    800: '#99BFFF',
  },
  
  // Green - Success states
  green: {
    950: '#EAFAF4',
    900: '#D4F5E9',
    800: '#A9EBD2',
    700: '#7FE0BC',
    600: '#54D6A5',
    500: '#2ACC8F',
    400: '#00C278',
    300: '#00A866',
    200: '#008A55',
    150: '#006B44',
  },
  
  // Evolved Green - Accent variant
  'evolved-green': {
    500: '#00B958',
    600: '#33C973',
    800: '#99E4B8',
  },
  
  // Quartz - Warm neutral
  quartz: {
    950: '#F9F7F4',
    900: '#F3EFE9',
    800: '#E7DFD2',
    700: '#DBCFBC',
    600: '#CFBFA5',
    500: '#C3AF8F',
    400: '#B79F78',
    300: '#AB8F62',
    200: '#9F7F4B',
    150: '#936F35',
  },
  
  // Evolved Quartz - Accent variant
  'evolved-quartz': {
    500: '#D4AF37',
    600: '#DCC255',
    800: '#EDDB99',
  },
  
  // Success - Semantic color
  success: {
    950: '#EAFAF4',
    900: '#D4F5E9',
    800: '#A9EBD2',
    700: '#7FE0BC',
    600: '#54D6A5',
    500: '#2ACC8F',
    400: '#00C278',
    300: '#00A866',
    200: '#008A55',
    150: '#006B44',
  },
  
  // Danger - Error states
  danger: {
    950: '#FFF5F5',
    900: '#FFEBEB',
    800: '#FFD6D6',
    700: '#FFC2C2',
    600: '#FFADAD',
    500: '#FF9999',
    400: '#FF8585',
    300: '#FF7070',
    200: '#FF5C5C',
    150: '#FF4747',
  },
} as const;

export type ColorScale = keyof typeof colors;
export type ColorShade = keyof typeof colors.coral;
