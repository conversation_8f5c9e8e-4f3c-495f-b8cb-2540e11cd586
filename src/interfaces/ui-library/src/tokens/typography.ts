/**
 * Typography tokens for Cohere Toolkit
 * Consolidated from both assistants_web and coral_web frontends
 */

export const fontFamily = {
  // Primary font stack as specified in requirements
  sans: [
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji',
  ],
  
  // Cohere brand fonts (when available)
  body: ['CohereText', 'Arial', 'ui-sans-serif', 'system-ui', 'sans-serif'],
  variable: ['CohereVariable', 'Arial', 'ui-serif', 'Georgia', 'serif'],
  code: ['CohereMono', 'ui-monospace', 'SFMono-Regular', 'Consolas', 'monospace'],
} as const;

export const fontWeight = {
  normal: '400',
  medium: '525', // Cohere Variable weight for bolded text
  semibold: '600',
  bold: '700',
} as const;

export const fontSize = {
  // Caption and small text
  'caption': ['0.75rem', { lineHeight: '1rem' }],      // 12px
  'label-sm': ['0.8125rem', { lineHeight: '1.125rem' }], // 13px
  'label': ['0.875rem', { lineHeight: '1.25rem' }],    // 14px
  'overline': ['0.875rem', { lineHeight: '1.25rem' }], // 14px
  
  // Paragraph text
  'p-xs': ['0.8125rem', { lineHeight: '1.25rem' }],    // 13px
  'p-sm': ['0.875rem', { lineHeight: '1.375rem' }],    // 14px
  'p': ['1rem', { lineHeight: '1.5rem' }],              // 16px
  'p-lg': ['1.125rem', { lineHeight: '1.75rem' }],     // 18px
  
  // Code text
  'code-sm': ['0.875rem', { lineHeight: '1.25rem' }],  // 14px
  'code': ['1rem', { lineHeight: '1.5rem' }],           // 16px
  
  // Logo and brand
  'logo': ['1.25rem', { lineHeight: '1.75rem' }],      // 20px
  
  // Headings
  'h5': ['1rem', { lineHeight: '1.5rem' }],             // 16px
  'h5-m': ['1rem', { lineHeight: '1.5rem', fontWeight: '525' }],
  'h4': ['1.125rem', { lineHeight: '1.75rem' }],       // 18px
  'h4-m': ['1.125rem', { lineHeight: '1.75rem', fontWeight: '525' }],
  'h3': ['1.25rem', { lineHeight: '1.75rem' }],        // 20px
  'h3-m': ['1.25rem', { lineHeight: '1.75rem', fontWeight: '525' }],
  'h2': ['1.5rem', { lineHeight: '2rem' }],            // 24px
  'h2-m': ['1.5rem', { lineHeight: '2rem', fontWeight: '525' }],
  'h1': ['1.875rem', { lineHeight: '2.25rem' }],       // 30px
  'h1-m': ['1.875rem', { lineHeight: '2.25rem', fontWeight: '525' }],
} as const;

export const lineHeight = {
  none: '1',
  tight: '1.25',
  snug: '1.375',
  normal: '1.5',
  relaxed: '1.625',
  loose: '2',
} as const;

export const letterSpacing = {
  tighter: '-0.05em',
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em',
} as const;

export type FontFamily = keyof typeof fontFamily;
export type FontWeight = keyof typeof fontWeight;
export type FontSize = keyof typeof fontSize;
