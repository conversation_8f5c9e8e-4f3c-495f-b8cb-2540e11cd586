@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Cohere brand colors */
    --coral-500: #FF2F00;
    --coral-400: #CC2500;
    --coral-600: #FF5833;

    /* Light theme */
    --background: #FAFAFA; /* marble-950 */
    --foreground: #39394A; /* volcanic-300 */
    --card: #FFFFFF; /* marble-1000 */
    --card-foreground: #39394A; /* volcanic-300 */
    --popover: #FFFFFF; /* marble-1000 */
    --popover-foreground: #39394A; /* volcanic-300 */
    --primary: #FF2F00; /* coral-500 */
    --primary-foreground: #FFFFFF;
    --secondary: #EDEDF0; /* volcanic-900 */
    --secondary-foreground: #39394A; /* volcanic-300 */
    --muted: #F7F7F8; /* volcanic-950 */
    --muted-foreground: #7C7C8A; /* volcanic-500 */
    --accent: #EDEDF0; /* volcanic-900 */
    --accent-foreground: #39394A; /* volcanic-300 */
    --destructive: #FF9999; /* danger-500 */
    --destructive-foreground: #FFFFFF;
    --border: #DCDCE3; /* volcanic-800 */
    --input: #FFFFFF;
    --ring: #FF2F00; /* coral-500 */
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark theme */
    --background: #13131F; /* volcanic-100 */
    --foreground: #FFFFFF;
    --card: #1F1F2E; /* volcanic-150 */
    --card-foreground: #FFFFFF;
    --popover: #1F1F2E; /* volcanic-150 */
    --popover-foreground: #FFFFFF;
    --primary: #FF2F00; /* coral-500 */
    --primary-foreground: #FFFFFF;
    --secondary: #2C2C3A; /* volcanic-200 */
    --secondary-foreground: #FFFFFF;
    --muted: #39394A; /* volcanic-300 */
    --muted-foreground: #9E9EAF; /* volcanic-600 */
    --accent: #2C2C3A; /* volcanic-200 */
    --accent-foreground: #FFFFFF;
    --destructive: #FF9999; /* danger-500 */
    --destructive-foreground: #FFFFFF;
    --border: #7C7C8A; /* volcanic-500 */
    --input: #2C2C3A; /* volcanic-200 */
    --ring: #FF2F00; /* coral-500 */ --sidebar-background: 240 5.9% 10%; --sidebar-foreground: 240 4.8% 95.9%; --sidebar-primary: 224.3 76.3% 48%; --sidebar-primary-foreground: 0 0% 100%; --sidebar-accent: 240 3.7% 15.9%; --sidebar-accent-foreground: 240 4.8% 95.9%; --sidebar-border: 240 3.7% 15.9%; --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-volcanic-800 dark:border-volcanic-500;
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Smooth theme transitions */
  * {
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
  }
}




