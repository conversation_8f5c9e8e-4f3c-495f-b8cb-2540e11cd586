// Common types for the UI library

export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export type Theme = 
  | 'default'
  | 'coral'
  | 'mushroom'
  | 'evolved-mushroom'
  | 'marble'
  | 'volcanic'
  | 'blue'
  | 'evolved-blue'
  | 'green'
  | 'evolved-green'
  | 'quartz'
  | 'evolved-quartz'
  | 'success'
  | 'danger';

export type ButtonKind = 'primary' | 'secondary' | 'outline' | 'cell';

export type InputKind = 'default' | 'cell';

export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}
