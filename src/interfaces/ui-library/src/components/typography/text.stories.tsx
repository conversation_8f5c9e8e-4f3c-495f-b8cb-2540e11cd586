import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Text } from './text';

const meta: Meta<typeof Text> = {
  title: 'Typography/Text',
  component: Text,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: [
        'caption', 'label-sm', 'label', 'overline',
        'p-xs', 'p-sm', 'p', 'p-lg',
        'code-sm', 'code',
        'logo',
        'h5', 'h5-m', 'h4', 'h4-m', 'h3', 'h3-m', 'h2', 'h2-m', 'h1', 'h1-m'
      ],
    },
    color: {
      control: 'select',
      options: ['default', 'muted', 'primary', 'secondary', 'success', 'danger', 'white', 'black'],
    },
    as: {
      control: 'select',
      options: ['p', 'span', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'This is the default text component using the Cohere font stack.',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="space-y-4 max-w-2xl">
      <div>
        <Text variant="h1" as="h1">Heading 1 - Large Title</Text>
        <Text variant="h1-m" as="h1">Heading 1 Medium - Large Title</Text>
      </div>
      
      <div>
        <Text variant="h2" as="h2">Heading 2 - Section Title</Text>
        <Text variant="h2-m" as="h2">Heading 2 Medium - Section Title</Text>
      </div>
      
      <div>
        <Text variant="h3" as="h3">Heading 3 - Subsection</Text>
        <Text variant="h3-m" as="h3">Heading 3 Medium - Subsection</Text>
      </div>
      
      <div>
        <Text variant="h4" as="h4">Heading 4 - Component Title</Text>
        <Text variant="h4-m" as="h4">Heading 4 Medium - Component Title</Text>
      </div>
      
      <div>
        <Text variant="h5" as="h5">Heading 5 - Small Title</Text>
        <Text variant="h5-m" as="h5">Heading 5 Medium - Small Title</Text>
      </div>
      
      <div>
        <Text variant="logo">Cohere Logo Text</Text>
      </div>
      
      <div>
        <Text variant="p-lg">Large paragraph text for important content and introductions.</Text>
        <Text variant="p">Regular paragraph text for body content and descriptions.</Text>
        <Text variant="p-sm">Small paragraph text for secondary information.</Text>
        <Text variant="p-xs">Extra small paragraph text for captions and notes.</Text>
      </div>
      
      <div>
        <Text variant="label">Form Label</Text>
        <Text variant="label-sm">Small Form Label</Text>
        <Text variant="caption">Caption text for images and small notes</Text>
        <Text variant="overline">Overline Text</Text>
      </div>
      
      <div>
        <Text variant="code">const example = "code text";</Text>
        <Text variant="code-sm">npm install @cohere-ai/ui-library</Text>
      </div>
    </div>
  ),
};

export const ColorVariants: Story = {
  render: () => (
    <div className="space-y-2">
      <Text color="default">Default text color</Text>
      <Text color="muted">Muted text color</Text>
      <Text color="primary">Primary text color (Coral)</Text>
      <Text color="secondary">Secondary text color</Text>
      <Text color="success">Success text color</Text>
      <Text color="danger">Danger text color</Text>
    </div>
  ),
};

export const FontStack: Story = {
  render: () => (
    <div className="space-y-4 max-w-2xl">
      <Text variant="h3" as="h3">Font Stack Demonstration</Text>
      <Text variant="p">
        This text uses the specified font stack: ui-sans-serif, system-ui, -apple-system, 
        BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", 
        sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"
      </Text>
      <Text variant="p">
        The font stack ensures consistent typography across different operating systems 
        and devices, providing the best available system font for optimal readability.
      </Text>
      <Text variant="code">
        font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 
        "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
      </Text>
    </div>
  ),
};
