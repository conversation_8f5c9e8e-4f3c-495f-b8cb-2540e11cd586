import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'

import { Text } from './text'

describe('Text', () => {
  it('renders correctly with default props', () => {
    render(<Text>Hello World</Text>)
    expect(screen.getByText('Hello World')).toBeInTheDocument()
  })

  it('renders with different variants', () => {
    const { rerender } = render(<Text variant="h1">Heading 1</Text>)
    expect(screen.getByText('Heading 1')).toHaveClass('text-h1')

    rerender(<Text variant="h2">Heading 2</Text>)
    expect(screen.getByText('Heading 2')).toHaveClass('text-h2')

    rerender(<Text variant="p">Paragraph</Text>)
    expect(screen.getByText('Paragraph')).toHaveClass('text-p')

    rerender(<Text variant="caption">Caption</Text>)
    expect(screen.getByText('Caption')).toHaveClass('text-caption')

    rerender(<Text variant="code">Code</Text>)
    expect(screen.getByText('Code')).toHaveClass('text-code', 'font-code')

    rerender(<Text variant="logo">Logo</Text>)
    expect(screen.getByText('Logo')).toHaveClass('text-logo', 'font-semibold')
  })

  it('renders with different colors', () => {
    const { rerender } = render(<Text color="default">Default</Text>)
    expect(screen.getByText('Default')).toHaveClass('text-foreground')

    rerender(<Text color="muted">Muted</Text>)
    expect(screen.getByText('Muted')).toHaveClass('text-muted-foreground')

    rerender(<Text color="primary">Primary</Text>)
    expect(screen.getByText('Primary')).toHaveClass('text-coral-500')

    rerender(<Text color="secondary">Secondary</Text>)
    expect(screen.getByText('Secondary')).toHaveClass('text-volcanic-500')

    rerender(<Text color="success">Success</Text>)
    expect(screen.getByText('Success')).toHaveClass('text-green-500')

    rerender(<Text color="danger">Danger</Text>)
    expect(screen.getByText('Danger')).toHaveClass('text-danger-500')

    rerender(<Text color="white">White</Text>)
    expect(screen.getByText('White')).toHaveClass('text-white')

    rerender(<Text color="black">Black</Text>)
    expect(screen.getByText('Black')).toHaveClass('text-black')
  })

  it('renders with different HTML elements', () => {
    const { rerender } = render(<Text as="h1">Heading</Text>)
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()

    rerender(<Text as="h2">Heading 2</Text>)
    expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument()

    rerender(<Text as="span">Span text</Text>)
    expect(screen.getByText('Span text').tagName).toBe('SPAN')

    rerender(<Text as="div">Div text</Text>)
    expect(screen.getByText('Div text').tagName).toBe('DIV')
  })

  it('applies custom className', () => {
    render(<Text className="custom-class">Custom</Text>)
    expect(screen.getByText('Custom')).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(<Text ref={ref}>Text with ref</Text>)
    expect(ref).toHaveBeenCalled()
  })

  it('passes through HTML attributes', () => {
    render(<Text data-testid="test-text" id="text-id">Test</Text>)
    const element = screen.getByTestId('test-text')
    expect(element).toHaveAttribute('id', 'text-id')
  })

  it('renders heading variants with proper styling', () => {
    const { rerender } = render(<Text variant="h1-m">H1 Medium</Text>)
    expect(screen.getByText('H1 Medium')).toHaveClass('text-h1', 'font-medium')

    rerender(<Text variant="h2-m">H2 Medium</Text>)
    expect(screen.getByText('H2 Medium')).toHaveClass('text-h2', 'font-medium')

    rerender(<Text variant="h3-m">H3 Medium</Text>)
    expect(screen.getByText('H3 Medium')).toHaveClass('text-h3', 'font-medium')

    rerender(<Text variant="h4-m">H4 Medium</Text>)
    expect(screen.getByText('H4 Medium')).toHaveClass('text-h4', 'font-medium')

    rerender(<Text variant="h5-m">H5 Medium</Text>)
    expect(screen.getByText('H5 Medium')).toHaveClass('text-h5', 'font-medium')
  })

  it('renders paragraph variants with proper sizing', () => {
    const { rerender } = render(<Text variant="p-xs">Extra small</Text>)
    expect(screen.getByText('Extra small')).toHaveClass('text-p-xs')

    rerender(<Text variant="p-sm">Small</Text>)
    expect(screen.getByText('Small')).toHaveClass('text-p-sm')

    rerender(<Text variant="p">Regular</Text>)
    expect(screen.getByText('Regular')).toHaveClass('text-p')

    rerender(<Text variant="p-lg">Large</Text>)
    expect(screen.getByText('Large')).toHaveClass('text-p-lg')
  })

  it('renders label variants correctly', () => {
    const { rerender } = render(<Text variant="label">Label</Text>)
    expect(screen.getByText('Label')).toHaveClass('text-label')

    rerender(<Text variant="label-sm">Small Label</Text>)
    expect(screen.getByText('Small Label')).toHaveClass('text-label-sm')
  })

  it('renders code variants with monospace font', () => {
    const { rerender } = render(<Text variant="code">const x = 1;</Text>)
    expect(screen.getByText('const x = 1;')).toHaveClass('text-code', 'font-code')

    rerender(<Text variant="code-sm">npm install</Text>)
    expect(screen.getByText('npm install')).toHaveClass('text-code-sm', 'font-code')
  })

  it('renders overline variant with uppercase styling', () => {
    render(<Text variant="overline">Overline Text</Text>)
    expect(screen.getByText('Overline Text')).toHaveClass('text-overline', 'uppercase', 'tracking-wide')
  })

  it('combines variant and color classes correctly', () => {
    render(<Text variant="h1" color="primary">Primary Heading</Text>)
    const element = screen.getByText('Primary Heading')
    expect(element).toHaveClass('text-h1', 'text-coral-500')
  })

  it('handles complex children content', () => {
    render(
      <Text variant="p">
        This is <strong>bold</strong> and <em>italic</em> text.
      </Text>
    )
    expect(screen.getByText(/This is/)).toBeInTheDocument()
    expect(screen.getByText('bold')).toBeInTheDocument()
    expect(screen.getByText('italic')).toBeInTheDocument()
  })

  it('renders with semantic HTML when using heading variants', () => {
    render(<Text variant="h1" as="h1">Page Title</Text>)
    expect(screen.getByRole('heading', { level: 1, name: 'Page Title' })).toBeInTheDocument()
  })

  it('maintains accessibility with proper semantic elements', () => {
    const { rerender } = render(<Text as="p">Paragraph content</Text>)
    expect(screen.getByText('Paragraph content').tagName).toBe('P')

    rerender(<Text as="span">Inline content</Text>)
    expect(screen.getByText('Inline content').tagName).toBe('SPAN')
  })
})
