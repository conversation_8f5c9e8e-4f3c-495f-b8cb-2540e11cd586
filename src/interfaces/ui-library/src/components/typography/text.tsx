import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/utils"

const textVariants = cva("", {
  variants: {
    variant: {
      // Caption and small text
      caption: "text-caption leading-4",
      "label-sm": "text-label-sm leading-[1.125rem]",
      label: "text-label leading-5",
      overline: "text-overline leading-5 uppercase tracking-wide",
      
      // Paragraph text
      "p-xs": "text-p-xs leading-5",
      "p-sm": "text-p-sm leading-[1.375rem]",
      p: "text-p leading-6",
      "p-lg": "text-p-lg leading-7",
      
      // Code text
      "code-sm": "text-code-sm leading-5 font-code",
      code: "text-code leading-6 font-code",
      
      // Logo and brand
      logo: "text-logo leading-7 font-semibold",
      
      // Headings
      h5: "text-h5 leading-6",
      "h5-m": "text-h5 leading-6 font-medium",
      h4: "text-h4 leading-7",
      "h4-m": "text-h4 leading-7 font-medium",
      h3: "text-h3 leading-7",
      "h3-m": "text-h3 leading-7 font-medium",
      h2: "text-h2 leading-8",
      "h2-m": "text-h2 leading-8 font-medium",
      h1: "text-h1 leading-9",
      "h1-m": "text-h1 leading-9 font-medium",
    },
    color: {
      default: "text-foreground",
      muted: "text-muted-foreground",
      primary: "text-coral-500",
      secondary: "text-volcanic-500 dark:text-volcanic-600",
      success: "text-green-500",
      danger: "text-danger-500",
      white: "text-white",
      black: "text-black",
    },
  },
  defaultVariants: {
    variant: "p",
    color: "default",
  },
})

export interface TextProps
  extends Omit<React.HTMLAttributes<HTMLElement>, 'color'>,
    VariantProps<typeof textVariants> {
  as?: keyof JSX.IntrinsicElements
  children: React.ReactNode
}

const Text = React.forwardRef<HTMLElement, TextProps>(
  ({ className, variant, color, as: Component = "p", children, ...props }, ref) => {
    return React.createElement(
      Component,
      {
        className: cn(textVariants({ variant, color, className })),
        ref,
        ...props,
      },
      children
    )
  }
)
Text.displayName = "Text"

export { Text, textVariants }
