import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Theme<PERSON>rovider, ThemeToggle, SimpleThemeToggle } from './index';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const meta: Meta<typeof ThemeProvider> = {
  title: 'Providers/ThemeProvider',
  component: ThemeProvider,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const ThemeDemo = () => (
  <div className="space-y-4 p-6">
    <div className="flex items-center justify-between">
      <h2 className="text-2xl font-bold">Theme Demo</h2>
      <div className="flex gap-2">
        <SimpleThemeToggle />
        <ThemeToggle />
      </div>
    </div>
    
    <Card className="w-[400px]">
      <CardHeader>
        <CardTitle>Theme System</CardTitle>
        <CardDescription>
          This demonstrates the theme switching capabilities of the UI library.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            Email
          </label>
          <Input id="email" placeholder="Enter your email" />
        </div>
        
        <div className="flex gap-2">
          <Button variant="default">Primary</Button>
          <Button variant="secondary">Secondary</Button>
          <Button variant="outline">Outline</Button>
        </div>
        
        <div className="rounded-lg border p-4">
          <p className="text-sm text-muted-foreground">
            This content adapts to the current theme. The colors, borders, and backgrounds
            all change seamlessly when switching between light and dark modes.
          </p>
        </div>
      </CardContent>
    </Card>
  </div>
);

export const Default: Story = {
  args: {
    attribute: 'class',
    defaultTheme: 'system',
    enableSystem: true,
    disableTransitionOnChange: false,
  },
  render: (args) => (
    <ThemeProvider {...args}>
      <ThemeDemo />
    </ThemeProvider>
  ),
};

export const LightTheme: Story = {
  args: {
    attribute: 'class',
    defaultTheme: 'light',
    enableSystem: false,
    disableTransitionOnChange: false,
  },
  render: (args) => (
    <ThemeProvider {...args}>
      <ThemeDemo />
    </ThemeProvider>
  ),
};

export const DarkTheme: Story = {
  args: {
    attribute: 'class',
    defaultTheme: 'dark',
    enableSystem: false,
    disableTransitionOnChange: false,
  },
  render: (args) => (
    <ThemeProvider {...args}>
      <ThemeDemo />
    </ThemeProvider>
  ),
};
