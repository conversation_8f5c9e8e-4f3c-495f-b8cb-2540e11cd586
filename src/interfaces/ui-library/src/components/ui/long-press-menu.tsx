"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/utils/index"
import {
  Dialog,
  DialogContent,
  DialogOverlay,
  DialogTitle,
} from "./dialog"

interface LongPressMenuItem {
  label: string
  onClick?: () => void
  icon?: React.ReactNode
  className?: string
  disabled?: boolean
  variant?: "default" | "destructive"
}

export type { LongPressMenuItem }

const longPressMenuVariants = cva(
  "fixed inset-x-0 bottom-0 z-50 grid w-full gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom sm:max-w-lg sm:rounded-t-lg",
  {
    variants: {
      size: {
        sm: "p-4 gap-2",
        md: "p-6 gap-4",
        lg: "p-8 gap-6",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

const longPressMenuItemVariants = cva(
  "flex w-full items-center gap-3 rounded-md px-4 py-3 text-left text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "hover:bg-accent hover:text-accent-foreground",
        destructive: "text-destructive hover:bg-destructive/10",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface LongPressMenuProps extends VariantProps<typeof longPressMenuVariants> {
  isOpen: boolean
  onClose: () => void
  items?: LongPressMenuItem[]
  children?: React.ReactNode
  className?: string
  overlayClassName?: string
  title?: string
}

const LongPressMenu = React.forwardRef<
  React.ElementRef<typeof DialogContent>,
  LongPressMenuProps
>(({
  isOpen,
  onClose,
  items,
  children,
  className,
  overlayClassName,
  size,
  title,
  ...props
}, ref) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogOverlay 
        className={cn(
          "fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          overlayClassName
        )}
      />
      <DialogContent
        ref={ref}
        className={cn(longPressMenuVariants({ size }), className)}
        position="bottom-sheet"
        showCloseButton={false}
        {...props}
      >
        <DialogTitle className={cn(
          "text-center text-lg font-semibold",
          !title && "sr-only"
        )}>
          {title || "Menu"}
        </DialogTitle>
        
        {children && (
          <div className="space-y-2">
            {children}
          </div>
        )}
        
        {items && items.length > 0 && (
          <div className="space-y-1">
            {items.map((item, index) => (
              <button
                key={`${item.label}-${index}`}
                onClick={() => {
                  item.onClick?.()
                  onClose()
                }}
                disabled={item.disabled}
                className={cn(
                  longPressMenuItemVariants({ variant: item.variant }),
                  item.className
                )}
              >
                {item.icon && (
                  <span className="flex h-5 w-5 items-center justify-center">
                    {item.icon}
                  </span>
                )}
                <span className="flex-1">{item.label}</span>
              </button>
            ))}
          </div>
        )}
        
        <button
          onClick={onClose}
          className={cn(
            longPressMenuItemVariants(),
            "mt-4 border-t pt-4 text-center justify-center"
          )}
        >
          Cancel
        </button>
      </DialogContent>
    </Dialog>
  )
})
LongPressMenu.displayName = "LongPressMenu"

// Individual menu item component for custom layouts
const LongPressMenuItem = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & 
    VariantProps<typeof longPressMenuItemVariants> & {
    icon?: React.ReactNode
  }
>(({ className, variant, icon, children, ...props }, ref) => (
  <button
    ref={ref}
    className={cn(longPressMenuItemVariants({ variant }), className)}
    {...props}
  >
    {icon && (
      <span className="flex h-5 w-5 items-center justify-center">
        {icon}
      </span>
    )}
    <span className="flex-1 text-left">{children}</span>
  </button>
))
LongPressMenuItem.displayName = "LongPressMenuItem"

export {
  LongPressMenu,
  longPressMenuVariants,
  longPressMenuItemVariants,
}
