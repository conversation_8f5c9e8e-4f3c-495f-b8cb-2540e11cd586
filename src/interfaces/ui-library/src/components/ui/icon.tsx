import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import * as LucideIcons from "lucide-react"

import { cn } from "@/utils/index"

// Define available icon names from Lucide
export type IconName = keyof typeof LucideIcons

export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'inherit'

const iconVariants = cva("", {
  variants: {
    size: {
      xs: "h-3 w-3",
      sm: "h-4 w-4", 
      md: "h-5 w-5",
      lg: "h-6 w-6",
      xl: "h-8 w-8",
      inherit: "h-inherit w-inherit",
    },
  },
  defaultVariants: {
    size: "md",
  },
})

export interface IconProps
  extends Omit<React.SVGProps<SVGSVGElement>, 'size'>,
    VariantProps<typeof iconVariants> {
  name: IconName
  size?: IconSize
}

const Icon = React.forwardRef<SVGSVGElement, IconProps>(
  ({ name, size, className, ...props }, ref) => {
    // Get the Lucide icon component
    const LucideIcon = LucideIcons[name] as React.ComponentType<React.SVGProps<SVGSVGElement>>
    
    if (!LucideIcon) {
      console.warn(`Icon "${name}" not found in Lucide icons`)
      return null
    }

    return (
      <LucideIcon
        ref={ref}
        className={cn(iconVariants({ size }), className)}
        aria-hidden="true"
        focusable="false"
        {...props}
      />
    )
  }
)
Icon.displayName = "Icon"

// Commonly used icons for easy access
export const CommonIcons = {
  // Navigation
  ArrowLeft: 'ArrowLeft' as IconName,
  ArrowRight: 'ArrowRight' as IconName,
  ArrowUp: 'ArrowUp' as IconName,
  ArrowDown: 'ArrowDown' as IconName,
  ChevronLeft: 'ChevronLeft' as IconName,
  ChevronRight: 'ChevronRight' as IconName,
  ChevronUp: 'ChevronUp' as IconName,
  ChevronDown: 'ChevronDown' as IconName,
  
  // Actions
  Plus: 'Plus' as IconName,
  Minus: 'Minus' as IconName,
  X: 'X' as IconName,
  Check: 'Check' as IconName,
  Copy: 'Copy' as IconName,
  Edit: 'Edit' as IconName,
  Trash: 'Trash2' as IconName,
  Download: 'Download' as IconName,
  Upload: 'Upload' as IconName,
  Share: 'Share' as IconName,
  
  // Interface
  Menu: 'Menu' as IconName,
  Search: 'Search' as IconName,
  Settings: 'Settings' as IconName,
  User: 'User' as IconName,
  Home: 'Home' as IconName,
  Bell: 'Bell' as IconName,
  
  // Content
  File: 'File' as IconName,
  Folder: 'Folder' as IconName,
  Image: 'Image' as IconName,
  Link: 'Link' as IconName,
  Mail: 'Mail' as IconName,
  MessageCircle: 'MessageCircle' as IconName,
  
  // Status
  Info: 'Info' as IconName,
  AlertCircle: 'AlertCircle' as IconName,
  AlertTriangle: 'AlertTriangle' as IconName,
  CheckCircle: 'CheckCircle' as IconName,
  XCircle: 'XCircle' as IconName,
  
  // Media
  Play: 'Play' as IconName,
  Pause: 'Pause' as IconName,
  Stop: 'Square' as IconName,
  Volume: 'Volume2' as IconName,
  
  // Theme
  Sun: 'Sun' as IconName,
  Moon: 'Moon' as IconName,
  
  // Loading
  Loader: 'Loader2' as IconName,
  RefreshCw: 'RefreshCw' as IconName,
  
  // Visibility
  Eye: 'Eye' as IconName,
  EyeOff: 'EyeOff' as IconName,
  
  // External
  ExternalLink: 'ExternalLink' as IconName,
  Github: 'Github' as IconName,
  
  // Code
  Code: 'Code' as IconName,
  Terminal: 'Terminal' as IconName,
  
  // Communication
  Send: 'Send' as IconName,
  Phone: 'Phone' as IconName,
  
  // Organization
  Calendar: 'Calendar' as IconName,
  Clock: 'Clock' as IconName,
  Star: 'Star' as IconName,
  Heart: 'Heart' as IconName,
  Bookmark: 'Bookmark' as IconName,
  
  // Data
  Database: 'Database' as IconName,
  BarChart: 'BarChart3' as IconName,
  PieChart: 'PieChart' as IconName,
  
  // Layout
  Grid: 'Grid3X3' as IconName,
  List: 'List' as IconName,
  Columns: 'Columns' as IconName,
  
  // Feedback
  ThumbsUp: 'ThumbsUp' as IconName,
  ThumbsDown: 'ThumbsDown' as IconName,
  
  // Tools
  Wrench: 'Wrench' as IconName,
  Hammer: 'Hammer' as IconName,
  Zap: 'Zap' as IconName,
  
  // Shapes
  Circle: 'Circle' as IconName,
  Square: 'Square' as IconName,
  Triangle: 'Triangle' as IconName,
} as const

export { Icon, iconVariants }
