"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/utils/index"
import { Label } from "./label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  selectTriggerVariants,
  selectContentVariants,
  selectItemVariants,
} from "./select"

export interface DropdownOption {
  value: string
  label?: string
  disabled?: boolean
}

const dropdownVariants = cva("", {
  variants: {
    size: {
      sm: "",
      md: "",
      lg: "",
    },
  },
  defaultVariants: {
    size: "md",
  },
})

export interface DropdownProps extends VariantProps<typeof dropdownVariants> {
  options: DropdownOption[]
  value?: string
  placeholder?: string
  label?: string
  className?: string
  triggerClassName?: string
  contentClassName?: string
  disabled?: boolean
  required?: boolean
  error?: string
  onChange: (value: string) => void
}

const Dropdown = React.forwardRef<
  React.ElementRef<typeof SelectTrigger>,
  DropdownProps
>(({
  options,
  value,
  placeholder = "Select an option",
  label,
  className,
  triggerClassName,
  contentClassName,
  size = "md",
  disabled = false,
  required = false,
  error,
  onChange,
  ...props
}, ref) => {
  const selectedOption = options.find((option) => option.value === value)

  return (
    <div className={cn("flex w-full flex-col gap-y-2", className)}>
      {label && (
        <Label className="flex items-start gap-x-2">
          <span className={cn(required && "after:content-['*'] after:ml-0.5 after:text-destructive")}>
            {label}
          </span>
        </Label>
      )}
      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger
          ref={ref}
          size={size}
          className={cn(
            error && "border-destructive focus:ring-destructive",
            triggerClassName
          )}
          {...props}
        >
          <SelectValue
            placeholder={placeholder}
            className={cn(
              !selectedOption && "text-muted-foreground"
            )}
          >
            {selectedOption?.label || selectedOption?.value || placeholder}
          </SelectValue>
        </SelectTrigger>
        <SelectContent size={size} className={contentClassName}>
          {options.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              size={size}
              disabled={option.disabled}
              className={cn(
                selectedOption?.value === option.value && "font-medium"
              )}
            >
              {option.label ?? option.value}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  )
})
Dropdown.displayName = "Dropdown"

export { Dropdown, dropdownVariants }
