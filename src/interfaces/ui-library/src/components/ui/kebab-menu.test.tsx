import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { KebabMenu } from './kebab-menu'

describe('KebabMenu', () => {
  const mockItems = [
    {
      label: 'Edit',
      onClick: vi.fn(),
    },
    {
      label: 'Copy',
      onClick: vi.fn(),
    },
    {
      label: 'Delete',
      onClick: vi.fn(),
      variant: 'destructive' as const,
    },
  ]

  beforeEach(() => {
    mockItems.forEach(item => item.onClick.mockClear())
  })

  it('renders trigger button', () => {
    render(<KebabMenu items={mockItems} />)
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    expect(trigger).toBeInTheDocument()
  })

  it('opens menu when trigger is clicked', async () => {
    const user = userEvent.setup()
    render(<KebabMenu items={mockItems} />)
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)
    
    await waitFor(() => {
      expect(screen.getByText('Edit')).toBeInTheDocument()
      expect(screen.getByText('Copy')).toBeInTheDocument()
      expect(screen.getByText('Delete')).toBeInTheDocument()
    })
  })

  it('calls onClick when menu item is clicked', async () => {
    const user = userEvent.setup()
    render(<KebabMenu items={mockItems} />)
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)
    
    await waitFor(() => {
      expect(screen.getByText('Edit')).toBeInTheDocument()
    })
    
    await user.click(screen.getByText('Edit'))
    expect(mockItems[0].onClick).toHaveBeenCalledTimes(1)
  })

  it('renders with different sizes', () => {
    const { rerender } = render(<KebabMenu items={mockItems} size="sm" />)
    let trigger = screen.getByRole('button', { name: /open menu/i })
    expect(trigger).toHaveClass('h-6', 'w-6')
    
    rerender(<KebabMenu items={mockItems} size="md" />)
    trigger = screen.getByRole('button', { name: /open menu/i })
    expect(trigger).toHaveClass('h-8', 'w-8')
    
    rerender(<KebabMenu items={mockItems} size="lg" />)
    trigger = screen.getByRole('button', { name: /open menu/i })
    expect(trigger).toHaveClass('h-10', 'w-10')
  })

  it('renders with ghost variant', () => {
    render(<KebabMenu items={mockItems} variant="ghost" />)

    const trigger = screen.getByRole('button', { name: /open menu/i })
    // Ghost variant uses the ghost button variant
    expect(trigger).toHaveClass('hover:bg-volcanic-900')
  })

  it('renders custom icon', () => {
    const customIcon = <span data-testid="custom-icon">Custom</span>
    render(<KebabMenu items={mockItems} icon={customIcon} />)
    
    expect(screen.getByTestId('custom-icon')).toBeInTheDocument()
  })

  it('handles disabled state', () => {
    render(<KebabMenu items={mockItems} disabled />)
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    expect(trigger).toBeDisabled()
  })

  it('filters out invisible items', async () => {
    const itemsWithHidden = [
      ...mockItems,
      {
        label: 'Hidden',
        onClick: vi.fn(),
        visible: false,
      },
    ]
    
    const user = userEvent.setup()
    render(<KebabMenu items={itemsWithHidden} />)
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)
    
    await waitFor(() => {
      expect(screen.getByText('Edit')).toBeInTheDocument()
    })
    
    expect(screen.queryByText('Hidden')).not.toBeInTheDocument()
  })

  it('renders nothing when all items are hidden', () => {
    const hiddenItems = [
      {
        label: 'Hidden 1',
        onClick: vi.fn(),
        visible: false,
      },
      {
        label: 'Hidden 2',
        onClick: vi.fn(),
        visible: false,
      },
    ]
    
    const { container } = render(<KebabMenu items={hiddenItems} />)
    expect(container.firstChild).toBeNull()
  })

  it('handles disabled menu items', async () => {
    const itemsWithDisabled = [
      {
        label: 'Enabled',
        onClick: vi.fn(),
      },
      {
        label: 'Disabled',
        onClick: vi.fn(),
        disabled: true,
      },
    ]

    const user = userEvent.setup()
    render(<KebabMenu items={itemsWithDisabled} />)

    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)

    await waitFor(() => {
      expect(screen.getByText('Disabled')).toBeInTheDocument()
    })

    const disabledItem = screen.getByText('Disabled').closest('[role="menuitem"]')
    expect(disabledItem).toHaveAttribute('data-disabled', 'true')
  })

  it('renders destructive variant items', async () => {
    const user = userEvent.setup()
    render(<KebabMenu items={mockItems} />)

    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)

    await waitFor(() => {
      expect(screen.getByText('Delete')).toBeInTheDocument()
    })

    const deleteItem = screen.getByText('Delete').closest('[role="menuitem"]')
    expect(deleteItem).toHaveClass('text-destructive')
  })

  it('renders with separators', async () => {
    const itemsWithSeparator = [
      {
        label: 'Edit',
        onClick: vi.fn(),
      },
      {
        label: 'Delete',
        onClick: vi.fn(),
        separator: true,
      },
    ]
    
    const user = userEvent.setup()
    render(<KebabMenu items={itemsWithSeparator} />)
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)
    
    await waitFor(() => {
      expect(screen.getByText('Edit')).toBeInTheDocument()
    })
    
    // Check for separator (role="separator" is added by DropdownMenuSeparator)
    const separators = screen.getAllByRole('separator')
    expect(separators.length).toBeGreaterThan(0)
  })

  it('renders with icons', async () => {
    const itemsWithIcons = [
      {
        label: 'Edit',
        icon: <span data-testid="edit-icon">✏️</span>,
        onClick: vi.fn(),
      },
    ]
    
    const user = userEvent.setup()
    render(<KebabMenu items={itemsWithIcons} />)
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)
    
    await waitFor(() => {
      expect(screen.getByTestId('edit-icon')).toBeInTheDocument()
    })
  })

  it('handles href items correctly', async () => {
    const itemsWithHref = [
      {
        label: 'External Link',
        href: 'https://example.com',
      },
    ]
    
    const user = userEvent.setup()
    render(<KebabMenu items={itemsWithHref} />)
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)
    
    await waitFor(() => {
      const link = screen.getByText('External Link').closest('a')
      expect(link).toHaveAttribute('href', 'https://example.com')
    })
  })

  it('applies custom positioning', async () => {
    const user = userEvent.setup()
    render(
      <KebabMenu 
        items={mockItems} 
        side="top" 
        align="start" 
        sideOffset={10}
      />
    )
    
    const trigger = screen.getByRole('button', { name: /open menu/i })
    await user.click(trigger)
    
    // Menu should be rendered (positioning is handled by Radix)
    await waitFor(() => {
      expect(screen.getByText('Edit')).toBeInTheDocument()
    })
  })
})
