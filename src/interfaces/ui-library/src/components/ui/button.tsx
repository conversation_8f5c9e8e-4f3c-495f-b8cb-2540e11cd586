import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-coral-500 text-white hover:bg-coral-400 dark:bg-coral-500 dark:hover:bg-coral-400",
        destructive:
          "bg-danger-500 text-white hover:bg-danger-400 dark:bg-danger-500 dark:hover:bg-danger-400",
        outline:
          "border border-volcanic-800 bg-transparent hover:bg-volcanic-900 hover:text-white dark:border-volcanic-500 dark:hover:bg-volcanic-200 dark:hover:text-volcanic-100",
        secondary:
          "bg-volcanic-900 text-volcanic-300 hover:bg-volcanic-800 dark:bg-volcanic-200 dark:text-volcanic-100 dark:hover:bg-volcanic-300",
        ghost: "hover:bg-volcanic-900 hover:text-white dark:hover:bg-volcanic-200 dark:hover:text-volcanic-100",
        link: "text-coral-500 underline-offset-4 hover:underline dark:text-coral-400",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
