import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { Loader2 } from "lucide-react"

import { cn } from "@/utils/index"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-coral-500 text-white hover:bg-coral-400 dark:bg-coral-500 dark:hover:bg-coral-400",
        destructive: "bg-danger-500 text-white hover:bg-danger-400 dark:bg-danger-500 dark:hover:bg-danger-400",
        outline: "border border-volcanic-800 bg-transparent hover:bg-volcanic-900 hover:text-white dark:border-volcanic-500 dark:hover:bg-volcanic-200 dark:hover:text-volcanic-100",
        secondary: "bg-volcanic-900 text-volcanic-300 hover:bg-volcanic-800 dark:bg-volcanic-200 dark:text-volcanic-100 dark:hover:bg-volcanic-300",
        ghost: "hover:bg-volcanic-900 hover:text-white dark:hover:bg-volcanic-200 dark:hover:text-volcanic-100",
        link: "text-coral-500 underline-offset-4 hover:underline dark:text-coral-400",
        cell: "h-cell-button bg-transparent border-0 p-0 hover:bg-transparent",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
      theme: {
        default: "",
        coral: "bg-coral-500 hover:bg-coral-400 text-white dark:bg-coral-500 dark:hover:bg-coral-400",
        "evolved-green": "bg-evolved-green-500 hover:bg-evolved-green-600 text-white dark:bg-evolved-green-500 dark:hover:bg-evolved-green-600",
        blue: "bg-blue-500 hover:bg-blue-400 text-white dark:bg-blue-500 dark:hover:bg-blue-400",
        "evolved-blue": "bg-evolved-blue-500 hover:bg-evolved-blue-600 text-white dark:bg-evolved-blue-500 dark:hover:bg-evolved-blue-600",
        green: "bg-green-500 hover:bg-green-400 text-white dark:bg-green-500 dark:hover:bg-green-400",
        quartz: "bg-quartz-500 hover:bg-quartz-400 text-white dark:bg-quartz-500 dark:hover:bg-quartz-400",
        "evolved-quartz": "bg-evolved-quartz-500 hover:bg-evolved-quartz-600 text-white dark:bg-evolved-quartz-500 dark:hover:bg-evolved-quartz-600",
        mushroom: "bg-mushroom-500 hover:bg-mushroom-400 text-white dark:bg-mushroom-500 dark:hover:bg-mushroom-400",
        "evolved-mushroom": "bg-evolved-mushroom-500 hover:bg-evolved-mushroom-600 text-white dark:bg-evolved-mushroom-500 dark:hover:bg-evolved-mushroom-600",
        danger: "bg-danger-500 hover:bg-danger-400 text-white dark:bg-danger-500 dark:hover:bg-danger-400",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      theme: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  isLoading?: boolean
  loadingText?: string
  icon?: React.ReactNode
  iconPosition?: "start" | "end"
  href?: string
  target?: string
  rel?: string
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant,
    size,
    theme,
    asChild = false,
    isLoading = false,
    loadingText,
    icon,
    iconPosition = "start",
    href,
    target,
    rel,
    children,
    disabled,
    ...props
  }, ref) => {
    const isDisabled = disabled || isLoading

    const content = (
      <>
        {isLoading && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        {!isLoading && icon && iconPosition === "start" && (
          <span className="mr-2">{icon}</span>
        )}
        {isLoading ? loadingText || children : children}
        {!isLoading && icon && iconPosition === "end" && (
          <span className="ml-2">{icon}</span>
        )}
      </>
    )

    const buttonClassName = cn(buttonVariants({ variant, size, theme, className }))

    if (asChild) {
      return (
        <Slot
          className={buttonClassName}
          ref={ref}
          {...props}
        >
          {content}
        </Slot>
      )
    }

    if (href && !isDisabled) {
      return (
        <a
          href={href}
          target={target}
          rel={rel}
          className={buttonClassName}
          {...(props as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
        >
          {content}
        </a>
      )
    }

    return (
      <button
        disabled={isDisabled}
        className={buttonClassName}
        ref={ref}
        {...props}
      >
        {content}
      </button>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
