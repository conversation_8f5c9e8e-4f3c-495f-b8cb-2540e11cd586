import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'

import { IconButton } from './icon-button'
import { CommonIcons } from './icon'

describe('IconButton', () => {
  it('renders correctly with required props', () => {
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" />)
    const button = screen.getByRole('button', { name: 'Home' })
    expect(button).toBeInTheDocument()
  })

  it('renders with different sizes', () => {
    const { rerender } = render(<IconButton icon={CommonIcons.Star} size="xs" aria-label="Star" />)
    let button = screen.getByRole('button', { name: 'Star' })
    expect(button).toHaveClass('h-6', 'w-6')

    rerender(<IconButton icon={CommonIcons.Star} size="sm" aria-label="Star" />)
    button = screen.getByRole('button', { name: 'Star' })
    expect(button).toHaveClass('h-8', 'w-8')

    rerender(<IconButton icon={CommonIcons.Star} size="md" aria-label="Star" />)
    button = screen.getByRole('button', { name: 'Star' })
    expect(button).toHaveClass('h-10', 'w-10')

    rerender(<IconButton icon={CommonIcons.Star} size="lg" aria-label="Star" />)
    button = screen.getByRole('button', { name: 'Star' })
    expect(button).toHaveClass('h-12', 'w-12')

    rerender(<IconButton icon={CommonIcons.Star} size="xl" aria-label="Star" />)
    button = screen.getByRole('button', { name: 'Star' })
    expect(button).toHaveClass('h-14', 'w-14')
  })

  it('renders with different variants', () => {
    const { rerender } = render(<IconButton icon={CommonIcons.Home} variant="default" aria-label="Home" />)
    let button = screen.getByRole('button', { name: 'Home' })
    expect(button).toBeInTheDocument()

    rerender(<IconButton icon={CommonIcons.Home} variant="outline" aria-label="Home" />)
    button = screen.getByRole('button', { name: 'Home' })
    expect(button).toHaveClass('border')

    rerender(<IconButton icon={CommonIcons.Home} variant="ghost" aria-label="Home" />)
    button = screen.getByRole('button', { name: 'Home' })
    expect(button).toBeInTheDocument()

    rerender(<IconButton icon={CommonIcons.Home} variant="secondary" aria-label="Home" />)
    button = screen.getByRole('button', { name: 'Home' })
    expect(button).toHaveClass('bg-secondary')
  })

  it('handles click events', async () => {
    const user = userEvent.setup()
    const handleClick = vi.fn()
    
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" onClick={handleClick} />)
    const button = screen.getByRole('button', { name: 'Home' })
    
    await user.click(button)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('can be disabled', () => {
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" disabled />)
    const button = screen.getByRole('button', { name: 'Home' })
    expect(button).toBeDisabled()
  })

  it('does not trigger click when disabled', async () => {
    const user = userEvent.setup()
    const handleClick = vi.fn()
    
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" onClick={handleClick} disabled />)
    const button = screen.getByRole('button', { name: 'Home' })
    
    await user.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('applies custom className', () => {
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" className="custom-class" />)
    const button = screen.getByRole('button', { name: 'Home' })
    expect(button).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" ref={ref} />)
    expect(ref).toHaveBeenCalled()
  })

  it('requires aria-label for accessibility', () => {
    render(<IconButton icon={CommonIcons.Home} aria-label="Home button" />)
    const button = screen.getByRole('button', { name: 'Home button' })
    expect(button).toHaveAttribute('aria-label', 'Home button')
  })

  it('uses custom icon size when provided', () => {
    const { container } = render(<IconButton icon={CommonIcons.Home} iconSize="xl" aria-label="Home" />)
    const icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-8', 'w-8') // xl icon size
  })

  it('maps button size to appropriate icon size', () => {
    const { rerender, container } = render(<IconButton icon={CommonIcons.Home} size="xs" aria-label="Home" />)
    let icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-3', 'w-3') // xs icon size

    rerender(<IconButton icon={CommonIcons.Home} size="sm" aria-label="Home" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-4', 'w-4') // sm icon size

    rerender(<IconButton icon={CommonIcons.Home} size="md" aria-label="Home" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-5', 'w-5') // md icon size

    rerender(<IconButton icon={CommonIcons.Home} size="lg" aria-label="Home" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-6', 'w-6') // lg icon size

    rerender(<IconButton icon={CommonIcons.Home} size="xl" aria-label="Home" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-8', 'w-8') // xl icon size
  })

  it('passes through button props', () => {
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" data-testid="test-button" />)
    const button = screen.getByTestId('test-button')
    expect(button).toBeInTheDocument()
  })

  it('renders different common icons', () => {
    const icons = [
      CommonIcons.Plus,
      CommonIcons.Edit,
      CommonIcons.Trash,
      CommonIcons.Search,
      CommonIcons.Settings,
    ]

    icons.forEach((iconName, index) => {
      const { unmount, container } = render(<IconButton icon={iconName} aria-label={`Icon ${index}`} />)
      expect(screen.getByRole('button')).toBeInTheDocument()
      expect(container.querySelector('svg')).toBeInTheDocument()
      unmount()
    })
  })

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup()
    const handleClick = vi.fn()
    
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" onClick={handleClick} />)
    const button = screen.getByRole('button', { name: 'Home' })
    
    button.focus()
    expect(button).toHaveFocus()
    
    await user.keyboard('{Enter}')
    expect(handleClick).toHaveBeenCalledTimes(1)
    
    await user.keyboard(' ')
    expect(handleClick).toHaveBeenCalledTimes(2)
  })

  it('uses default variant and size when not specified', () => {
    render(<IconButton icon={CommonIcons.Home} aria-label="Home" />)
    const button = screen.getByRole('button', { name: 'Home' })
    expect(button).toHaveClass('h-10', 'w-10') // md size
  })

  it('combines variant and size classes correctly', () => {
    render(<IconButton icon={CommonIcons.Home} variant="outline" size="lg" aria-label="Home" />)
    const button = screen.getByRole('button', { name: 'Home' })
    expect(button).toHaveClass('h-12', 'w-12', 'border')
  })

  it('applies disabled styling to icon', () => {
    const { container } = render(<IconButton icon={CommonIcons.Home} aria-label="Home" disabled />)
    const icon = container.querySelector('svg')
    expect(icon).toHaveClass('opacity-50')
  })

  it('handles form submission', () => {
    render(
      <form data-testid="test-form">
        <IconButton icon={CommonIcons.Check} aria-label="Submit" type="submit" />
      </form>
    )
    const button = screen.getByRole('button', { name: 'Submit' })
    expect(button).toHaveAttribute('type', 'submit')
  })
})
