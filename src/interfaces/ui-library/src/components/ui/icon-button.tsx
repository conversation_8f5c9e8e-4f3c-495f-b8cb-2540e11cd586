import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/utils/index"
import { Button, type ButtonProps } from "./button"
import { Icon, type IconName, type IconSize } from "./icon"

const iconButtonVariants = cva("", {
  variants: {
    size: {
      xs: "h-6 w-6 p-1",
      sm: "h-8 w-8 p-1.5", 
      md: "h-10 w-10 p-2",
      lg: "h-12 w-12 p-2.5",
      xl: "h-14 w-14 p-3",
    },
    variant: {
      default: "",
      outline: "border border-input",
      ghost: "hover:bg-accent hover:text-accent-foreground",
      secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    },
  },
  defaultVariants: {
    size: "md",
    variant: "ghost",
  },
})

export interface IconButtonProps
  extends Omit<ButtonProps, 'children' | 'size' | 'variant'>,
    VariantProps<typeof iconButtonVariants> {
  icon: IconName
  iconSize?: IconSize
  'aria-label': string // Required for accessibility
}

const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ 
    icon, 
    iconSize, 
    size, 
    variant, 
    className, 
    disabled,
    'aria-label': ariaLabel,
    ...props 
  }, ref) => {
    // Map button size to appropriate icon size
    const getIconSize = (): IconSize => {
      if (iconSize) return iconSize
      
      switch (size) {
        case 'xs': return 'xs'
        case 'sm': return 'sm'
        case 'md': return 'md'
        case 'lg': return 'lg'
        case 'xl': return 'xl'
        default: return 'md'
      }
    }

    // Map IconButton variant to Button variant
    const buttonVariant = variant === 'default' ? 'outline' : variant

    return (
      <Button
        ref={ref}
        variant={buttonVariant}
        className={cn(iconButtonVariants({ size, variant }), className)}
        disabled={disabled}
        aria-label={ariaLabel}
        {...props}
      >
        <Icon 
          name={icon} 
          size={getIconSize()}
          className={cn(
            "transition-colors",
            disabled && "opacity-50"
          )}
        />
      </Button>
    )
  }
)
IconButton.displayName = "IconButton"

export { IconButton, iconButtonVariants }
