import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Modal, ConfirmModal } from './modal';
import { Button } from './button';
import { Input } from './input';
import { Label } from './label';
import { Textarea } from './textarea';

const meta: Meta<typeof Modal> = {
  title: 'UI/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl', 'full'],
    },
    position: {
      control: 'select',
      options: ['center', 'top', 'bottom', 'bottom-sheet'],
    },
    spacing: {
      control: 'select',
      options: ['none', 'sm', 'md', 'lg', 'xl'],
    },
    overlayVariant: {
      control: 'select',
      options: ['default', 'light', 'dark', 'blur'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: (args) => {
    const [open, setOpen] = useState(false);

    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal
          {...args}
          open={open}
          onOpenChange={setOpen}
          title="Default Modal"
          description="This is a simple modal with title and description."
          footer={
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setOpen(false)}>
                Confirm
              </Button>
            </div>
          }
        >
          <p>This is the modal content area where you can put any content.</p>
        </Modal>
      </>
    );
  },
  args: {
    size: 'md',
    position: 'center',
    spacing: 'md',
  },
};

export const Sizes: Story = {
  render: () => {
    const [openModal, setOpenModal] = useState<string | null>(null);

    return (
      <div className="flex gap-4">
        {(['sm', 'md', 'lg', 'xl', 'full'] as const).map((size) => (
          <div key={size}>
            <Button onClick={() => setOpenModal(size)}>
              {size.toUpperCase()}
            </Button>
            <Modal
              open={openModal === size}
              onOpenChange={(open) => !open && setOpenModal(null)}
              size={size}
              title={`${size.toUpperCase()} Modal`}
              description={`This is a ${size} sized modal.`}
              footer={
                <Button onClick={() => setOpenModal(null)}>
                  Close
                </Button>
              }
            >
              <p>Content for {size} modal.</p>
            </Modal>
          </div>
        ))}
      </div>
    );
  },
};

export const Positions: Story = {
  render: () => {
    const [openModal, setOpenModal] = useState<string | null>(null);

    return (
      <div className="flex gap-4">
        {(['center', 'top', 'bottom', 'bottom-sheet'] as const).map((position) => (
          <div key={position}>
            <Button onClick={() => setOpenModal(position)}>
              {position.replace('-', ' ')}
            </Button>
            <Modal
              open={openModal === position}
              onOpenChange={(open) => !open && setOpenModal(null)}
              position={position}
              title={`${position} Modal`}
              description={`This modal appears at ${position}.`}
              footer={
                <Button onClick={() => setOpenModal(null)}>
                  Close
                </Button>
              }
            >
              <p>Content positioned at {position}.</p>
            </Modal>
          </div>
        ))}
      </div>
    );
  },
};

export const FormModal: Story = {
  render: () => {
    const [open, setOpen] = useState(false);
    const [formData, setFormData] = useState({
      name: '',
      email: '',
      message: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      console.log('Form submitted:', formData);
      setOpen(false);
    };

    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Form Modal</Button>
        <Modal
          open={open}
          onOpenChange={setOpen}
          title="Contact Form"
          description="Fill out the form below to send us a message."
          size="lg"
          footer={
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" form="contact-form">
                Send Message
              </Button>
            </div>
          }
        >
          <form id="contact-form" onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Your name"
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Your message..."
                rows={4}
              />
            </div>
          </form>
        </Modal>
      </>
    );
  },
};

export const ConfirmationModal: Story = {
  render: () => {
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const handleConfirm = async () => {
      setLoading(true);
      // Simulate async operation
      await new Promise(resolve => setTimeout(resolve, 2000));
      setLoading(false);
      setOpen(false);
    };

    return (
      <>
        <Button variant="destructive" onClick={() => setOpen(true)}>
          Delete Account
        </Button>
        <ConfirmModal
          open={open}
          onOpenChange={setOpen}
          title="Delete Account"
          description="Are you sure you want to delete your account? This action cannot be undone."
          confirmText="Delete Account"
          cancelText="Cancel"
          confirmVariant="destructive"
          loading={loading}
          onConfirm={handleConfirm}
        />
      </>
    );
  },
};

export const CustomHeader: Story = {
  render: () => {
    const [open, setOpen] = useState(false);

    return (
      <>
        <Button onClick={() => setOpen(true)}>Custom Header</Button>
        <Modal
          open={open}
          onOpenChange={setOpen}
          header={
            <div className="flex items-center gap-3 p-6 border-b">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                U
              </div>
              <div>
                <h2 className="text-lg font-semibold">User Profile</h2>
                <p className="text-sm text-muted-foreground">Manage your account settings</p>
              </div>
            </div>
          }
          footer={
            <Button onClick={() => setOpen(false)}>
              Close
            </Button>
          }
        >
          <div className="space-y-4">
            <div>
              <Label>Username</Label>
              <Input defaultValue="johndoe" />
            </div>
            <div>
              <Label>Email</Label>
              <Input defaultValue="<EMAIL>" />
            </div>
          </div>
        </Modal>
      </>
    );
  },
};

export const NoCloseButton: Story = {
  render: () => {
    const [open, setOpen] = useState(false);

    return (
      <>
        <Button onClick={() => setOpen(true)}>Modal without Close Button</Button>
        <Modal
          open={open}
          onOpenChange={setOpen}
          showCloseButton={false}
          title="Important Notice"
          description="This modal requires you to make a choice."
          footer={
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                Decline
              </Button>
              <Button onClick={() => setOpen(false)}>
                Accept
              </Button>
            </div>
          }
        >
          <p>You must choose one of the options below to continue.</p>
        </Modal>
      </>
    );
  },
};

export const BottomSheet: Story = {
  render: () => {
    const [open, setOpen] = useState(false);

    return (
      <>
        <Button onClick={() => setOpen(true)}>Open Bottom Sheet</Button>
        <Modal
          open={open}
          onOpenChange={setOpen}
          position="bottom-sheet"
          title="Mobile Actions"
          description="Choose an action from the options below."
          footer={
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setOpen(false)}>
                Confirm
              </Button>
            </div>
          }
        >
          <div className="space-y-2">
            <Button variant="ghost" className="w-full justify-start">
              📷 Take Photo
            </Button>
            <Button variant="ghost" className="w-full justify-start">
              📁 Choose from Library
            </Button>
            <Button variant="ghost" className="w-full justify-start">
              📄 Upload Document
            </Button>
          </div>
        </Modal>
      </>
    );
  },
};
