import * as React from "react"
import { <PERSON>, <PERSON>Off, Copy, Check, Search, Loader2 } from "lucide-react"

import { cn } from "@/utils/index"
import { Text } from "@/components/typography"

export type InputActionType = 'copy' | 'reveal' | 'search' | 'loading' | 'success'
export type InputSize = 'sm' | 'md' | 'lg'
export type InputVariant = 'default' | 'cell'

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: React.ReactNode
  description?: string
  errorText?: string
  hasError?: boolean
  actionType?: InputActionType
  size?: InputSize
  variant?: InputVariant
  isLoading?: boolean
  onCopy?: () => void | Promise<void>
  onSearch?: (value: string) => void | Promise<void>
  containerClassName?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    containerClassName,
    type = "text",
    label,
    description,
    errorText,
    hasError = false,
    actionType,
    size = "md",
    variant = "default",
    isLoading = false,
    disabled = false,
    value,
    onCopy,
    onSearch,
    ...props
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const [copySuccess, setCopySuccess] = React.useState(false)

    const inputId = props.id || props.name || React.useId()
    const isError = hasError || !!errorText
    const isPasswordType = type === 'password'
    const actualType = (isPasswordType && actionType === 'reveal' && showPassword) ? 'text' : type

    // Handle copy action
    const handleCopy = React.useCallback(async () => {
      if (!value) return

      try {
        await navigator.clipboard.writeText(String(value))
        setCopySuccess(true)
        onCopy?.()
        setTimeout(() => setCopySuccess(false), 1000)
      } catch (error) {
        console.error('Failed to copy:', error)
      }
    }, [value, onCopy])

    // Handle search action
    const handleSearch = React.useCallback(() => {
      if (onSearch && value) {
        onSearch(String(value))
      }
    }, [onSearch, value])

    // Size classes
    const sizeClasses = {
      sm: "h-9 px-3 py-2 text-sm",
      md: "h-10 px-3 py-2 text-sm",
      lg: "h-11 px-4 py-3 text-base"
    }

    // Variant classes
    const variantClasses = {
      default: cn(
        "rounded-md border border-volcanic-800 dark:border-volcanic-500",
        "bg-white dark:bg-volcanic-100",
        "focus:bg-volcanic-950 dark:focus:bg-volcanic-150",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-coral-500 focus-visible:ring-offset-2"
      ),
      cell: cn(
        "rounded-lg border border-volcanic-800 dark:border-volcanic-500",
        "bg-marble-1000 dark:bg-volcanic-100",
        "focus-visible:outline-none"
      )
    }

    const inputClasses = cn(
      "flex w-full font-medium ring-offset-background",
      "file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground",
      "placeholder:text-volcanic-500 dark:placeholder:text-volcanic-600",
      "disabled:cursor-not-allowed disabled:bg-volcanic-800 disabled:text-volcanic-300",
      "dark:disabled:bg-volcanic-300 dark:disabled:text-volcanic-600",
      sizeClasses[size],
      variantClasses[variant],
      {
        "pr-10": actionType, // Space for action icon
        "border-danger-500 text-danger-500 dark:border-danger-500": isError,
        "bg-danger-950 dark:bg-danger-950": isError && variant === 'cell',
      },
      className
    )

    // Render action icon
    const renderActionIcon = () => {
      if (isLoading || actionType === 'loading') {
        return <Loader2 className="h-4 w-4 animate-spin text-volcanic-500" />
      }

      switch (actionType) {
        case 'copy':
          return (
            <button
              type="button"
              onClick={handleCopy}
              disabled={!value || disabled}
              className="flex items-center justify-center text-volcanic-500 hover:text-volcanic-300 disabled:opacity-50"
            >
              {copySuccess ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </button>
          )
        case 'reveal':
          return (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              disabled={disabled}
              className="flex items-center justify-center text-volcanic-500 hover:text-volcanic-300 disabled:opacity-50"
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          )
        case 'search':
          return (
            <button
              type="button"
              onClick={handleSearch}
              disabled={!value || disabled}
              className="flex items-center justify-center text-volcanic-500 hover:text-volcanic-300 disabled:opacity-50"
            >
              <Search className="h-4 w-4" />
            </button>
          )
        case 'success':
          return <Check className="h-4 w-4 text-success-500" />
        default:
          return null
      }
    }

    return (
      <div className={cn("flex flex-col gap-2", containerClassName)}>
        {label && (
          <label htmlFor={inputId} className="flex items-start gap-2">
            {typeof label === 'string' ? (
              <Text variant="label" className={cn(isError && "text-danger-500")}>
                {label}
              </Text>
            ) : (
              label
            )}
          </label>
        )}

        <div className="relative flex items-center">
          <input
            id={inputId}
            ref={ref}
            type={actualType}
            disabled={disabled || isLoading}
            value={value}
            className={inputClasses}
            {...props}
          />

          {actionType && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {renderActionIcon()}
            </div>
          )}
        </div>

        {(isError && errorText) || description ? (
          <Text
            variant="caption"
            className={cn(
              isError ? "text-danger-500" : "text-volcanic-400"
            )}
          >
            {isError ? errorText : description}
          </Text>
        ) : null}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
