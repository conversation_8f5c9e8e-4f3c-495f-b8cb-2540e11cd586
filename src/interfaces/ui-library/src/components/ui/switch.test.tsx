import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'

import { Switch } from './switch'

describe('Switch', () => {
  it('renders correctly with default props', () => {
    render(<Switch />)
    const switchElement = screen.getByRole('switch')
    expect(switchElement).toBeInTheDocument()
    expect(switchElement).not.toBeChecked()
  })

  it('renders as checked when checked prop is true', () => {
    render(<Switch checked />)
    const switchElement = screen.getByRole('switch')
    expect(switchElement).toBeChecked()
  })

  it('can be disabled', () => {
    render(<Switch disabled />)
    const switchElement = screen.getByRole('switch')
    expect(switchElement).toBeDisabled()
  })

  it('handles click events', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Switch onCheckedChange={handleChange} />)
    const switchElement = screen.getByRole('switch')
    
    await user.click(switchElement)
    expect(handleChange).toHaveBeenCalledWith(true)
  })

  it('does not trigger change when disabled', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Switch disabled onCheckedChange={handleChange} />)
    const switchElement = screen.getByRole('switch')
    
    await user.click(switchElement)
    expect(handleChange).not.toHaveBeenCalled()
  })

  it('renders with different sizes', () => {
    const { rerender } = render(<Switch size="sm" />)
    let switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('h-4', 'w-7')

    rerender(<Switch size="md" />)
    switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('h-5', 'w-9')

    rerender(<Switch size="lg" />)
    switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('h-6', 'w-11')
  })

  it('renders with different variants', () => {
    const { rerender } = render(<Switch variant="default" />)
    let switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('data-[state=checked]:bg-coral-500')

    rerender(<Switch variant="secondary" />)
    switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('data-[state=checked]:bg-secondary')

    rerender(<Switch variant="success" />)
    switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('data-[state=checked]:bg-green-500')
  })

  it('applies custom className', () => {
    render(<Switch className="custom-class" />)
    const switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(<Switch ref={ref} />)
    expect(ref).toHaveBeenCalled()
  })

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Switch onCheckedChange={handleChange} />)
    const switchElement = screen.getByRole('switch')
    
    switchElement.focus()
    expect(switchElement).toHaveFocus()
    
    await user.keyboard(' ')
    expect(handleChange).toHaveBeenCalledWith(true)
  })

  it('handles controlled state correctly', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    const { rerender } = render(
      <Switch checked={false} onCheckedChange={handleChange} />
    )
    
    let switchElement = screen.getByRole('switch')
    expect(switchElement).not.toBeChecked()
    
    await user.click(switchElement)
    expect(handleChange).toHaveBeenCalledWith(true)
    
    // Simulate parent component updating the state
    rerender(<Switch checked={true} onCheckedChange={handleChange} />)
    switchElement = screen.getByRole('switch')
    expect(switchElement).toBeChecked()
  })

  it('works with form submission', () => {
    render(
      <form data-testid="test-form">
        <Switch name="test-switch" value="test-value" />
      </form>
    )

    const switchElement = screen.getByRole('switch')
    // Radix components handle form submission internally
    expect(switchElement).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<Switch aria-label="Test switch" />)
    const switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveAttribute('aria-label', 'Test switch')
  })

  it('supports uncontrolled usage', async () => {
    const user = userEvent.setup()
    
    render(<Switch defaultChecked={false} />)
    const switchElement = screen.getByRole('switch')
    
    expect(switchElement).not.toBeChecked()
    
    await user.click(switchElement)
    expect(switchElement).toBeChecked()
    
    await user.click(switchElement)
    expect(switchElement).not.toBeChecked()
  })

  it('maintains focus styles', () => {
    render(<Switch />)
    const switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('focus-visible:ring-2')
  })

  it('renders with proper size classes', () => {
    const { rerender } = render(<Switch size="sm" />)
    let switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('h-4', 'w-7')

    rerender(<Switch size="md" />)
    switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('h-5', 'w-9')

    rerender(<Switch size="lg" />)
    switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('h-6', 'w-11')
  })

  it('toggles between checked and unchecked states', async () => {
    const user = userEvent.setup()
    
    render(<Switch />)
    const switchElement = screen.getByRole('switch')
    
    expect(switchElement).not.toBeChecked()
    expect(switchElement).toHaveAttribute('data-state', 'unchecked')
    
    await user.click(switchElement)
    expect(switchElement).toBeChecked()
    expect(switchElement).toHaveAttribute('data-state', 'checked')
    
    await user.click(switchElement)
    expect(switchElement).not.toBeChecked()
    expect(switchElement).toHaveAttribute('data-state', 'unchecked')
  })

  it('supports Enter key activation', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Switch onCheckedChange={handleChange} />)
    const switchElement = screen.getByRole('switch')
    
    switchElement.focus()
    await user.keyboard('{Enter}')
    expect(handleChange).toHaveBeenCalledWith(true)
  })

  it('prevents interaction when disabled', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Switch disabled onCheckedChange={handleChange} />)
    const switchElement = screen.getByRole('switch')
    
    // Try clicking
    await user.click(switchElement)
    expect(handleChange).not.toHaveBeenCalled()
    
    // Try keyboard activation
    switchElement.focus()
    await user.keyboard(' ')
    expect(handleChange).not.toHaveBeenCalled()
    
    await user.keyboard('{Enter}')
    expect(handleChange).not.toHaveBeenCalled()
  })

  it('applies disabled styling', () => {
    render(<Switch disabled />)
    const switchElement = screen.getByRole('switch')
    expect(switchElement).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-50')
  })
})
