import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'

import { Checkbox } from './checkbox'

describe('Checkbox', () => {
  it('renders correctly with default props', () => {
    render(<Checkbox />)
    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toBeInTheDocument()
    expect(checkbox).not.toBeChecked()
  })

  it('renders as checked when checked prop is true', () => {
    render(<Checkbox checked />)
    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toBeChecked()
  })

  it('renders as indeterminate when indeterminate prop is true', () => {
    render(<Checkbox indeterminate />)
    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toHaveAttribute('data-state', 'indeterminate')
  })

  it('can be disabled', () => {
    render(<Checkbox disabled />)
    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toBeDisabled()
  })

  it('handles click events', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Checkbox onCheckedChange={handleChange} />)
    const checkbox = screen.getByRole('checkbox')
    
    await user.click(checkbox)
    expect(handleChange).toHaveBeenCalledWith(true)
  })

  it('does not trigger change when disabled', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Checkbox disabled onCheckedChange={handleChange} />)
    const checkbox = screen.getByRole('checkbox')
    
    await user.click(checkbox)
    expect(handleChange).not.toHaveBeenCalled()
  })

  it('renders with different sizes', () => {
    const { rerender } = render(<Checkbox size="sm" />)
    let checkbox = screen.getByRole('checkbox')
    expect(checkbox).toHaveClass('h-3.5', 'w-3.5')

    rerender(<Checkbox size="md" />)
    checkbox = screen.getByRole('checkbox')
    expect(checkbox).toHaveClass('h-4', 'w-4')
  })

  it('renders with different variants', () => {
    const { rerender } = render(<Checkbox variant="default" />)
    let checkbox = screen.getByRole('checkbox')
    expect(checkbox).toHaveClass('data-[state=checked]:bg-coral-500')

    rerender(<Checkbox variant="secondary" />)
    checkbox = screen.getByRole('checkbox')
    expect(checkbox).toHaveClass('data-[state=checked]:bg-secondary')
  })

  it('applies custom className', () => {
    render(<Checkbox className="custom-class" />)
    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(<Checkbox ref={ref} />)
    expect(ref).toHaveBeenCalled()
  })

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Checkbox onCheckedChange={handleChange} />)
    const checkbox = screen.getByRole('checkbox')
    
    checkbox.focus()
    expect(checkbox).toHaveFocus()
    
    await user.keyboard(' ')
    expect(handleChange).toHaveBeenCalledWith(true)
  })

  it('shows check icon when checked', () => {
    const { container } = render(<Checkbox checked />)
    const checkIcon = container.querySelector('svg')
    expect(checkIcon).toBeInTheDocument()
  })

  it('shows minus icon when indeterminate', () => {
    const { container } = render(<Checkbox indeterminate />)
    const minusIcon = container.querySelector('svg')
    expect(minusIcon).toBeInTheDocument()
  })

  it('handles controlled state correctly', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    const { rerender } = render(
      <Checkbox checked={false} onCheckedChange={handleChange} />
    )
    
    let checkbox = screen.getByRole('checkbox')
    expect(checkbox).not.toBeChecked()
    
    await user.click(checkbox)
    expect(handleChange).toHaveBeenCalledWith(true)
    
    // Simulate parent component updating the state
    rerender(<Checkbox checked={true} onCheckedChange={handleChange} />)
    checkbox = screen.getByRole('checkbox')
    expect(checkbox).toBeChecked()
  })

  it('works with form submission', () => {
    render(
      <form data-testid="test-form">
        <Checkbox name="test-checkbox" value="test-value" />
      </form>
    )

    const checkbox = screen.getByRole('checkbox')
    // Radix components handle form submission internally
    expect(checkbox).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<Checkbox aria-label="Test checkbox" />)
    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toHaveAttribute('aria-label', 'Test checkbox')
  })

  it('supports uncontrolled usage', async () => {
    const user = userEvent.setup()
    
    render(<Checkbox defaultChecked={false} />)
    const checkbox = screen.getByRole('checkbox')
    
    expect(checkbox).not.toBeChecked()
    
    await user.click(checkbox)
    expect(checkbox).toBeChecked()
    
    await user.click(checkbox)
    expect(checkbox).not.toBeChecked()
  })

  it('maintains focus styles', () => {
    render(<Checkbox />)
    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toHaveClass('focus-visible:ring-2')
  })

  it('handles indeterminate to checked transition', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Checkbox indeterminate onCheckedChange={handleChange} />)
    const checkbox = screen.getByRole('checkbox')
    
    expect(checkbox).toHaveAttribute('data-state', 'indeterminate')
    
    await user.click(checkbox)
    expect(handleChange).toHaveBeenCalledWith(true)
  })

  it('applies correct icon sizes for different checkbox sizes', () => {
    const { container: smallContainer } = render(<Checkbox size="sm" checked />)
    const smallIcon = smallContainer.querySelector('svg')
    expect(smallIcon).toHaveClass('h-2.5', 'w-2.5')

    const { container: mediumContainer } = render(<Checkbox size="md" checked />)
    const mediumIcon = mediumContainer.querySelector('svg')
    expect(mediumIcon).toHaveClass('h-3', 'w-3')
  })
})
