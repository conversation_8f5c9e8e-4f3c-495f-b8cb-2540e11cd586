import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/utils/index"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  type DialogContentProps,
} from "./dialog"

const modalVariants = cva(
  "flex flex-col",
  {
    variants: {
      spacing: {
        none: "gap-0",
        sm: "gap-2",
        md: "gap-4",
        lg: "gap-6",
        xl: "gap-8",
      },
    },
    defaultVariants: {
      spacing: "md",
    },
  }
)

export interface ModalProps extends Omit<DialogContentProps, 'children'> {
  /**
   * Whether the modal is open
   */
  open?: boolean
  /**
   * Callback fired when the modal should close
   */
  onOpenChange?: (open: boolean) => void
  /**
   * Modal title
   */
  title?: string
  /**
   * Modal description
   */
  description?: React.ReactNode
  /**
   * Modal content
   */
  children?: React.ReactNode
  /**
   * Footer content (typically buttons)
   */
  footer?: React.ReactNode
  /**
   * Spacing between modal sections
   */
  spacing?: VariantProps<typeof modalVariants>['spacing']
  /**
   * Whether to show the close button
   */
  showCloseButton?: boolean
  /**
   * Custom header content (overrides title/description)
   */
  header?: React.ReactNode
  /**
   * Additional className for the modal container
   */
  containerClassName?: string
}

/**
 * A unified Modal component that provides a simple API for creating modals.
 * Built on top of the enhanced Dialog component with support for different sizes,
 * positions, and styling variants.
 */
export const Modal = React.forwardRef<
  React.ElementRef<typeof DialogContent>,
  ModalProps
>(({
  open,
  onOpenChange,
  title,
  description,
  children,
  footer,
  spacing = "md",
  showCloseButton = true,
  header,
  containerClassName,
  className,
  ...props
}, ref) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        ref={ref}
        showCloseButton={showCloseButton}
        className={cn(modalVariants({ spacing }), className)}
        {...props}
      >
        {header ? (
          header
        ) : (title || description) ? (
          <DialogHeader>
            {title && <DialogTitle>{title}</DialogTitle>}
            {description && <DialogDescription>{description}</DialogDescription>}
          </DialogHeader>
        ) : null}
        
        {children && (
          <div className={cn("flex-1", containerClassName)}>
            {children}
          </div>
        )}
        
        {footer && (
          <DialogFooter>
            {footer}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  )
})
Modal.displayName = "Modal"

// Convenience components for common modal patterns
export interface ConfirmModalProps extends Omit<ModalProps, 'footer'> {
  /**
   * Confirm button text
   */
  confirmText?: string
  /**
   * Cancel button text  
   */
  cancelText?: string
  /**
   * Callback fired when confirm is clicked
   */
  onConfirm?: () => void
  /**
   * Callback fired when cancel is clicked
   */
  onCancel?: () => void
  /**
   * Whether the confirm action is loading
   */
  loading?: boolean
  /**
   * Confirm button variant
   */
  confirmVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
}

/**
 * A confirmation modal with confirm/cancel buttons
 */
export const ConfirmModal = React.forwardRef<
  React.ElementRef<typeof Modal>,
  ConfirmModalProps
>(({
  confirmText = "Confirm",
  cancelText = "Cancel", 
  onConfirm,
  onCancel,
  loading = false,
  confirmVariant = "default",
  onOpenChange,
  ...props
}, ref) => {
  const handleCancel = () => {
    onCancel?.()
    onOpenChange?.(false)
  }

  const handleConfirm = () => {
    onConfirm?.()
    if (!loading) {
      onOpenChange?.(false)
    }
  }

  return (
    <Modal
      ref={ref}
      onOpenChange={onOpenChange}
      footer={
        <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
          <button
            type="button"
            onClick={handleCancel}
            disabled={loading}
            className="mt-2 sm:mt-0 inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 sm:text-sm"
          >
            {cancelText}
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            disabled={loading}
            className={cn(
              "inline-flex justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 sm:text-sm",
              confirmVariant === "destructive" 
                ? "bg-red-600 hover:bg-red-700 focus:ring-red-500"
                : "bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500"
            )}
          >
            {loading ? "Loading..." : confirmText}
          </button>
        </div>
      }
      {...props}
    />
  )
})
ConfirmModal.displayName = "ConfirmModal"

export { modalVariants }
