import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { RadioGroup, RadioGroupItem } from './radio-group';
import { Label } from './label';

const meta: Meta<typeof RadioGroup> = {
  title: 'UI/RadioGroup',
  component: RadioGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    orientation: {
      control: 'select',
      options: ['vertical', 'horizontal'],
    },
    disabled: {
      control: 'boolean',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <RadioGroup defaultValue="option-one">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option-one" id="option-one" />
        <Label htmlFor="option-one">Option One</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option-two" id="option-two" />
        <Label htmlFor="option-two">Option Two</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option-three" id="option-three" />
        <Label htmlFor="option-three">Option Three</Label>
      </div>
    </RadioGroup>
  ),
};

export const Horizontal: Story = {
  render: () => (
    <RadioGroup defaultValue="option-one" orientation="horizontal">
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option-one" id="h-option-one" />
        <Label htmlFor="h-option-one">Option One</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option-two" id="h-option-two" />
        <Label htmlFor="h-option-two">Option Two</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option-three" id="h-option-three" />
        <Label htmlFor="h-option-three">Option Three</Label>
      </div>
    </RadioGroup>
  ),
};

export const Sizes: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="mb-3 text-sm font-medium">Small</h3>
        <RadioGroup defaultValue="small-one">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="small-one" id="small-one" size="sm" />
            <Label htmlFor="small-one">Small Option</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="small-two" id="small-two" size="sm" />
            <Label htmlFor="small-two">Small Option Two</Label>
          </div>
        </RadioGroup>
      </div>
      
      <div>
        <h3 className="mb-3 text-sm font-medium">Medium</h3>
        <RadioGroup defaultValue="medium-one">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="medium-one" id="medium-one" size="md" />
            <Label htmlFor="medium-one">Medium Option</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="medium-two" id="medium-two" size="md" />
            <Label htmlFor="medium-two">Medium Option Two</Label>
          </div>
        </RadioGroup>
      </div>
      
      <div>
        <h3 className="mb-3 text-sm font-medium">Large</h3>
        <RadioGroup defaultValue="large-one">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="large-one" id="large-one" size="lg" />
            <Label htmlFor="large-one">Large Option</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="large-two" id="large-two" size="lg" />
            <Label htmlFor="large-two">Large Option Two</Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  ),
};

export const Variants: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="mb-3 text-sm font-medium">Default</h3>
        <RadioGroup defaultValue="default-one">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="default-one" id="default-one" variant="default" />
            <Label htmlFor="default-one">Default Option</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="default-two" id="default-two" variant="default" />
            <Label htmlFor="default-two">Default Option Two</Label>
          </div>
        </RadioGroup>
      </div>
      
      <div>
        <h3 className="mb-3 text-sm font-medium">Secondary</h3>
        <RadioGroup defaultValue="secondary-one">
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="secondary-one" id="secondary-one" variant="secondary" />
            <Label htmlFor="secondary-one">Secondary Option</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="secondary-two" id="secondary-two" variant="secondary" />
            <Label htmlFor="secondary-two">Secondary Option Two</Label>
          </div>
        </RadioGroup>
      </div>
    </div>
  ),
};

export const Disabled: Story = {
  render: () => (
    <RadioGroup defaultValue="option-one" disabled>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option-one" id="disabled-one" />
        <Label htmlFor="disabled-one">Disabled Option One</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="option-two" id="disabled-two" />
        <Label htmlFor="disabled-two">Disabled Option Two</Label>
      </div>
    </RadioGroup>
  ),
};

export const Interactive: Story = {
  render: () => {
    const [value, setValue] = useState('option-one');
    
    return (
      <div className="space-y-4">
        <RadioGroup value={value} onValueChange={setValue}>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="option-one" id="interactive-one" />
            <Label htmlFor="interactive-one">Option One</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="option-two" id="interactive-two" />
            <Label htmlFor="interactive-two">Option Two</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="option-three" id="interactive-three" />
            <Label htmlFor="interactive-three">Option Three</Label>
          </div>
        </RadioGroup>
        <p className="text-sm text-muted-foreground">
          Selected: {value}
        </p>
      </div>
    );
  },
};

export const FormExample: Story = {
  render: () => {
    const [plan, setPlan] = useState('pro');
    
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Choose your plan</h3>
          <p className="text-sm text-muted-foreground">
            Select the plan that best fits your needs.
          </p>
        </div>
        
        <RadioGroup value={plan} onValueChange={setPlan}>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="free" id="plan-free" />
            <div className="grid gap-1.5 leading-none">
              <Label htmlFor="plan-free" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Free
              </Label>
              <p className="text-xs text-muted-foreground">
                Perfect for getting started. Includes basic features.
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="pro" id="plan-pro" />
            <div className="grid gap-1.5 leading-none">
              <Label htmlFor="plan-pro" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Pro
              </Label>
              <p className="text-xs text-muted-foreground">
                For professionals. Includes advanced features and priority support.
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="enterprise" id="plan-enterprise" />
            <div className="grid gap-1.5 leading-none">
              <Label htmlFor="plan-enterprise" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Enterprise
              </Label>
              <p className="text-xs text-muted-foreground">
                For large teams. Includes all features and dedicated support.
              </p>
            </div>
          </div>
        </RadioGroup>
        
        <div className="rounded-lg border p-4">
          <h4 className="font-medium">Selected Plan: {plan}</h4>
          <p className="text-sm text-muted-foreground mt-1">
            {plan === 'free' && 'Free plan with basic features'}
            {plan === 'pro' && 'Pro plan with advanced features - $29/month'}
            {plan === 'enterprise' && 'Enterprise plan with all features - Contact sales'}
          </p>
        </div>
      </div>
    );
  },
};
