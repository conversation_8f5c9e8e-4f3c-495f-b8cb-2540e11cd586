import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Icon, CommonIcons } from './icon';

const meta: Meta<typeof Icon> = {
  title: 'UI/Icon',
  component: Icon,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'select',
      options: Object.values(CommonIcons),
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl', 'inherit'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: CommonIcons.Home,
    size: 'md',
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <div className="flex flex-col items-center gap-2">
        <Icon name={CommonIcons.Star} size="xs" />
        <span className="text-xs">xs</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Icon name={CommonIcons.Star} size="sm" />
        <span className="text-xs">sm</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Icon name={CommonIcons.Star} size="md" />
        <span className="text-xs">md</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Icon name={CommonIcons.Star} size="lg" />
        <span className="text-xs">lg</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <Icon name={CommonIcons.Star} size="xl" />
        <span className="text-xs">xl</span>
      </div>
    </div>
  ),
};

export const CommonIconsShowcase: Story = {
  render: () => (
    <div className="grid grid-cols-8 gap-4 max-w-4xl">
      {Object.entries(CommonIcons).map(([key, iconName]) => (
        <div key={key} className="flex flex-col items-center gap-2 p-2">
          <Icon name={iconName} size="lg" />
          <span className="text-xs text-center">{key}</span>
        </div>
      ))}
    </div>
  ),
};

export const Navigation: Story = {
  render: () => (
    <div className="flex gap-4">
      <Icon name={CommonIcons.ArrowLeft} size="md" />
      <Icon name={CommonIcons.ArrowRight} size="md" />
      <Icon name={CommonIcons.ArrowUp} size="md" />
      <Icon name={CommonIcons.ArrowDown} size="md" />
      <Icon name={CommonIcons.ChevronLeft} size="md" />
      <Icon name={CommonIcons.ChevronRight} size="md" />
      <Icon name={CommonIcons.ChevronUp} size="md" />
      <Icon name={CommonIcons.ChevronDown} size="md" />
    </div>
  ),
};

export const Actions: Story = {
  render: () => (
    <div className="flex gap-4">
      <Icon name={CommonIcons.Plus} size="md" />
      <Icon name={CommonIcons.Minus} size="md" />
      <Icon name={CommonIcons.X} size="md" />
      <Icon name={CommonIcons.Check} size="md" />
      <Icon name={CommonIcons.Copy} size="md" />
      <Icon name={CommonIcons.Edit} size="md" />
      <Icon name={CommonIcons.Trash} size="md" />
      <Icon name={CommonIcons.Download} size="md" />
      <Icon name={CommonIcons.Upload} size="md" />
      <Icon name={CommonIcons.Share} size="md" />
    </div>
  ),
};

export const Status: Story = {
  render: () => (
    <div className="flex gap-4">
      <Icon name={CommonIcons.Info} size="md" className="text-blue-500" />
      <Icon name={CommonIcons.CheckCircle} size="md" className="text-green-500" />
      <Icon name={CommonIcons.AlertTriangle} size="md" className="text-yellow-500" />
      <Icon name={CommonIcons.XCircle} size="md" className="text-red-500" />
      <Icon name={CommonIcons.AlertCircle} size="md" className="text-orange-500" />
    </div>
  ),
};

export const WithCustomColors: Story = {
  render: () => (
    <div className="flex gap-4">
      <Icon name={CommonIcons.Heart} size="lg" className="text-red-500" />
      <Icon name={CommonIcons.Star} size="lg" className="text-yellow-500" />
      <Icon name={CommonIcons.ThumbsUp} size="lg" className="text-green-500" />
      <Icon name={CommonIcons.Github} size="lg" className="text-gray-800 dark:text-gray-200" />
      <Icon name={CommonIcons.Zap} size="lg" className="text-purple-500" />
    </div>
  ),
};

export const InheritSize: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="text-sm">
        Small text with <Icon name={CommonIcons.Star} size="inherit" className="inline" /> inherit size
      </div>
      <div className="text-lg">
        Large text with <Icon name={CommonIcons.Star} size="inherit" className="inline" /> inherit size
      </div>
      <div className="text-2xl">
        Extra large text with <Icon name={CommonIcons.Star} size="inherit" className="inline" /> inherit size
      </div>
    </div>
  ),
};

export const Loading: Story = {
  render: () => (
    <div className="flex gap-4">
      <Icon name={CommonIcons.Loader} size="md" className="animate-spin" />
      <Icon name={CommonIcons.RefreshCw} size="md" className="animate-spin" />
    </div>
  ),
};
