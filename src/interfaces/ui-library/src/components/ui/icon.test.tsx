import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'

import { Icon, CommonIcons } from './icon'

// Mock console.warn to test error handling
const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {})

describe('Icon', () => {
  afterEach(() => {
    mockConsoleWarn.mockClear()
  })

  it('renders correctly with default props', () => {
    const { container } = render(<Icon name={CommonIcons.Home} />)
    const icon = container.querySelector('svg')
    expect(icon).toBeInTheDocument()
  })

  it('renders with different sizes', () => {
    const { rerender, container } = render(<Icon name={CommonIcons.Star} size="xs" />)
    let icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-3', 'w-3')

    rerender(<Icon name={CommonIcons.Star} size="sm" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-4', 'w-4')

    rerender(<Icon name={CommonIcons.Star} size="md" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-5', 'w-5')

    rerender(<Icon name={CommonIcons.Star} size="lg" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-6', 'w-6')

    rerender(<Icon name={CommonIcons.Star} size="xl" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-8', 'w-8')

    rerender(<Icon name={CommonIcons.Star} size="inherit" />)
    icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-inherit', 'w-inherit')
  })

  it('applies custom className', () => {
    const { container } = render(<Icon name={CommonIcons.Home} className="custom-class" />)
    const icon = container.querySelector('svg')
    expect(icon).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(<Icon name={CommonIcons.Home} ref={ref} />)
    expect(ref).toHaveBeenCalled()
  })

  it('passes through SVG props', () => {
    render(<Icon name={CommonIcons.Home} data-testid="test-icon" />)
    const icon = screen.getByTestId('test-icon')
    expect(icon).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    const { container } = render(<Icon name={CommonIcons.Home} />)
    const icon = container.querySelector('svg')
    expect(icon).toHaveAttribute('aria-hidden', 'true')
    expect(icon).toHaveAttribute('focusable', 'false')
  })

  it('renders common icons correctly', () => {
    const { rerender, container } = render(<Icon name={CommonIcons.Home} />)
    expect(container.querySelector('svg')).toBeInTheDocument()

    rerender(<Icon name={CommonIcons.Star} />)
    expect(container.querySelector('svg')).toBeInTheDocument()

    rerender(<Icon name={CommonIcons.Search} />)
    expect(container.querySelector('svg')).toBeInTheDocument()

    rerender(<Icon name={CommonIcons.User} />)
    expect(container.querySelector('svg')).toBeInTheDocument()
  })

  it('handles invalid icon names gracefully', () => {
    render(<Icon name={'InvalidIcon' as any} />)
    expect(mockConsoleWarn).toHaveBeenCalledWith('Icon "InvalidIcon" not found in Lucide icons')
  })

  it('returns null for invalid icon names', () => {
    const { container } = render(<Icon name={'InvalidIcon' as any} />)
    expect(container.firstChild).toBeNull()
  })

  it('combines size and custom classes correctly', () => {
    const { container } = render(<Icon name={CommonIcons.Home} size="lg" className="text-red-500" />)
    const icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-6', 'w-6', 'text-red-500')
  })

  it('uses default size when not specified', () => {
    const { container } = render(<Icon name={CommonIcons.Home} />)
    const icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-5', 'w-5') // md is default
  })

  it('renders navigation icons', () => {
    const navigationIcons = [
      CommonIcons.ArrowLeft,
      CommonIcons.ArrowRight,
      CommonIcons.ArrowUp,
      CommonIcons.ArrowDown,
      CommonIcons.ChevronLeft,
      CommonIcons.ChevronRight,
      CommonIcons.ChevronUp,
      CommonIcons.ChevronDown,
    ]

    navigationIcons.forEach((iconName) => {
      const { unmount, container } = render(<Icon name={iconName} />)
      expect(container.querySelector('svg')).toBeInTheDocument()
      unmount()
    })
  })

  it('renders action icons', () => {
    const actionIcons = [
      CommonIcons.Plus,
      CommonIcons.Minus,
      CommonIcons.X,
      CommonIcons.Check,
      CommonIcons.Copy,
      CommonIcons.Edit,
      CommonIcons.Trash,
      CommonIcons.Download,
      CommonIcons.Upload,
      CommonIcons.Share,
    ]

    actionIcons.forEach((iconName) => {
      const { unmount, container } = render(<Icon name={iconName} />)
      expect(container.querySelector('svg')).toBeInTheDocument()
      unmount()
    })
  })

  it('renders status icons', () => {
    const statusIcons = [
      CommonIcons.Info,
      CommonIcons.AlertCircle,
      CommonIcons.AlertTriangle,
      CommonIcons.CheckCircle,
      CommonIcons.XCircle,
    ]

    statusIcons.forEach((iconName) => {
      const { unmount, container } = render(<Icon name={iconName} />)
      expect(container.querySelector('svg')).toBeInTheDocument()
      unmount()
    })
  })

  it('supports custom styling', () => {
    const { container } = render(<Icon name={CommonIcons.Heart} className="text-red-500 hover:text-red-600" />)
    const icon = container.querySelector('svg')
    expect(icon).toHaveClass('text-red-500', 'hover:text-red-600')
  })

  it('maintains aspect ratio with inherit size', () => {
    const { container } = render(
      <div className="text-2xl">
        <Icon name={CommonIcons.Star} size="inherit" />
      </div>
    )
    const icon = container.querySelector('svg')
    expect(icon).toHaveClass('h-inherit', 'w-inherit')
  })
})
