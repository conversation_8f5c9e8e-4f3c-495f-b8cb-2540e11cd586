import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { Heart } from 'lucide-react'

import { Button } from './button'

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('renders as disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled button</Button>)
    expect(screen.getByRole('button')).toBeDisabled()
  })

  it('renders as disabled when isLoading is true', () => {
    render(<Button isLoading>Loading button</Button>)
    expect(screen.getByRole('button')).toBeDisabled()
  })

  it('shows loading spinner when isLoading is true', () => {
    render(<Button isLoading>Loading</Button>)
    expect(screen.getByRole('button')).toHaveTextContent('Loading')
    // Check for spinner (Loader2 icon)
    expect(document.querySelector('.animate-spin')).toBeInTheDocument()
  })

  it('shows custom loading text when provided', () => {
    render(<Button isLoading loadingText="Saving...">Save</Button>)
    expect(screen.getByRole('button')).toHaveTextContent('Saving...')
  })

  it('renders with different variants', () => {
    const { rerender } = render(<Button variant="default">Default</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-coral-500')

    rerender(<Button variant="destructive">Destructive</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-danger-500')

    rerender(<Button variant="outline">Outline</Button>)
    expect(screen.getByRole('button')).toHaveClass('border')

    rerender(<Button variant="secondary">Secondary</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-volcanic-900')

    rerender(<Button variant="ghost">Ghost</Button>)
    expect(screen.getByRole('button')).toHaveClass('hover:bg-volcanic-900')

    rerender(<Button variant="link">Link</Button>)
    expect(screen.getByRole('button')).toHaveClass('underline-offset-4')
  })

  it('renders with different sizes', () => {
    const { rerender } = render(<Button size="sm">Small</Button>)
    expect(screen.getByRole('button')).toHaveClass('h-9')

    rerender(<Button size="default">Default</Button>)
    expect(screen.getByRole('button')).toHaveClass('h-10')

    rerender(<Button size="lg">Large</Button>)
    expect(screen.getByRole('button')).toHaveClass('h-11')

    rerender(<Button size="icon">Icon</Button>)
    expect(screen.getByRole('button')).toHaveClass('h-10', 'w-10')
  })

  it('renders with different themes', () => {
    const { rerender } = render(<Button theme="coral">Coral</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-coral-500')

    rerender(<Button theme="evolved-green">Evolved Green</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-evolved-green-500')

    rerender(<Button theme="danger">Danger</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-danger-500')
  })

  it('renders with start icon', () => {
    render(
      <Button icon={<Heart data-testid="heart-icon" />} iconPosition="start">
        With Icon
      </Button>
    )
    
    const button = screen.getByRole('button')
    const icon = screen.getByTestId('heart-icon')
    
    expect(button).toContainElement(icon)
    expect(icon.parentElement).toHaveClass('mr-2')
  })

  it('renders with end icon', () => {
    render(
      <Button icon={<Heart data-testid="heart-icon" />} iconPosition="end">
        With Icon
      </Button>
    )
    
    const button = screen.getByRole('button')
    const icon = screen.getByTestId('heart-icon')
    
    expect(button).toContainElement(icon)
    expect(icon.parentElement).toHaveClass('ml-2')
  })

  it('renders as link when href is provided', () => {
    render(<Button href="/test">Link Button</Button>)
    
    const link = screen.getByRole('link', { name: 'Link Button' })
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute('href', '/test')
  })

  it('renders as button when href is provided but disabled', () => {
    render(<Button href="/test" disabled>Disabled Link</Button>)
    
    expect(screen.getByRole('button')).toBeInTheDocument()
    expect(screen.queryByRole('link')).not.toBeInTheDocument()
  })

  it('applies custom className', () => {
    render(<Button className="custom-class">Custom</Button>)
    expect(screen.getByRole('button')).toHaveClass('custom-class')
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(<Button ref={ref}>Button with ref</Button>)
    expect(ref).toHaveBeenCalled()
  })

  it('does not call onClick when disabled', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick} disabled>Disabled</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('does not call onClick when loading', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick} isLoading>Loading</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).not.toHaveBeenCalled()
  })
})
