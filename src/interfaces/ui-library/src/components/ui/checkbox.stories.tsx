import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Checkbox } from './checkbox';
import { Label } from './label';

const meta: Meta<typeof Checkbox> = {
  title: 'UI/Checkbox',
  component: Checkbox,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    checked: {
      control: 'boolean',
    },
    disabled: {
      control: 'boolean',
    },
    indeterminate: {
      control: 'boolean',
    },
    size: {
      control: 'select',
      options: ['sm', 'md'],
    },
    variant: {
      control: 'select',
      options: ['default', 'secondary'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    checked: false,
    disabled: false,
    indeterminate: false,
    size: 'md',
    variant: 'default',
  },
};

export const Checked: Story = {
  args: {
    checked: true,
  },
};

export const Indeterminate: Story = {
  args: {
    indeterminate: true,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const DisabledChecked: Story = {
  args: {
    checked: true,
    disabled: true,
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-2">
        <Checkbox id="small" size="sm" />
        <Label htmlFor="small">Small</Label>
      </div>
      <div className="flex items-center gap-2">
        <Checkbox id="medium" size="md" />
        <Label htmlFor="medium">Medium</Label>
      </div>
    </div>
  ),
};

export const Variants: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <div className="flex items-center gap-2">
        <Checkbox id="default" variant="default" checked />
        <Label htmlFor="default">Default</Label>
      </div>
      <div className="flex items-center gap-2">
        <Checkbox id="secondary" variant="secondary" checked />
        <Label htmlFor="secondary">Secondary</Label>
      </div>
    </div>
  ),
};

export const WithLabel: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Checkbox id="terms" />
      <Label
        htmlFor="terms"
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        Accept terms and conditions
      </Label>
    </div>
  ),
};

export const Interactive: Story = {
  render: () => {
    const [checked, setChecked] = useState(false);
    
    return (
      <div className="flex items-center space-x-2">
        <Checkbox 
          id="interactive" 
          checked={checked}
          onCheckedChange={setChecked}
        />
        <Label htmlFor="interactive">
          {checked ? 'Checked' : 'Unchecked'}
        </Label>
      </div>
    );
  },
};

export const FormExample: Story = {
  render: () => {
    const [preferences, setPreferences] = useState({
      marketing: false,
      analytics: true,
      functional: true,
    });

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Cookie Preferences</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="marketing"
              checked={preferences.marketing}
              onCheckedChange={(checked) => 
                setPreferences(prev => ({ ...prev, marketing: checked }))
              }
            />
            <Label htmlFor="marketing">Marketing cookies</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="analytics"
              checked={preferences.analytics}
              onCheckedChange={(checked) => 
                setPreferences(prev => ({ ...prev, analytics: checked }))
              }
            />
            <Label htmlFor="analytics">Analytics cookies</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="functional"
              checked={preferences.functional}
              onCheckedChange={(checked) => 
                setPreferences(prev => ({ ...prev, functional: checked }))
              }
            />
            <Label htmlFor="functional">Functional cookies</Label>
          </div>
        </div>
      </div>
    );
  },
};

export const IndeterminateExample: Story = {
  render: () => {
    const [items, setItems] = useState([
      { id: 1, label: 'Item 1', checked: true },
      { id: 2, label: 'Item 2', checked: false },
      { id: 3, label: 'Item 3', checked: true },
    ]);

    const checkedCount = items.filter(item => item.checked).length;
    const isIndeterminate = checkedCount > 0 && checkedCount < items.length;
    const isAllChecked = checkedCount === items.length;

    const handleSelectAll = (checked: boolean) => {
      setItems(items.map(item => ({ ...item, checked })));
    };

    const handleItemChange = (id: number, checked: boolean) => {
      setItems(items.map(item => 
        item.id === id ? { ...item, checked } : item
      ));
    };

    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={isAllChecked}
            indeterminate={isIndeterminate}
            onCheckedChange={handleSelectAll}
          />
          <Label className="font-medium">Select All</Label>
        </div>
        <div className="ml-6 space-y-2">
          {items.map(item => (
            <div key={item.id} className="flex items-center space-x-2">
              <Checkbox
                checked={item.checked}
                onCheckedChange={(checked) => handleItemChange(item.id, checked)}
              />
              <Label>{item.label}</Label>
            </div>
          ))}
        </div>
      </div>
    );
  },
};
