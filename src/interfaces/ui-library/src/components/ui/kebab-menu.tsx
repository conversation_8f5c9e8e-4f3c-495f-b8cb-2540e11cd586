"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { MoreVertical } from "lucide-react"

import { cn } from "@/utils/index"
import { Button } from "./button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./dropdown-menu"

export interface KebabMenuItem {
  label: string
  onClick?: () => void
  href?: string
  icon?: React.ReactNode
  className?: string
  visible?: boolean
  disabled?: boolean
  variant?: "default" | "destructive"
  separator?: boolean
}

const kebabMenuVariants = cva("", {
  variants: {
    size: {
      sm: "",
      md: "",
      lg: "",
    },
    variant: {
      default: "",
      ghost: "",
    },
  },
  defaultVariants: {
    size: "md",
    variant: "default",
  },
})

export interface KebabMenuProps extends VariantProps<typeof kebabMenuVariants> {
  items: KebabMenuItem[]
  className?: string
  triggerClassName?: string
  contentClassName?: string
  side?: "top" | "right" | "bottom" | "left"
  align?: "start" | "center" | "end"
  sideOffset?: number
  disabled?: boolean
  icon?: React.ReactNode
}

const KebabMenu = React.forwardRef<
  React.ElementRef<typeof DropdownMenuTrigger>,
  KebabMenuProps
>(({
  items,
  className,
  triggerClassName,
  contentClassName,
  size = "md",
  variant = "default",
  side = "bottom",
  align = "end",
  sideOffset = 4,
  disabled = false,
  icon,
  ...props
}, ref) => {
  const visibleItems = items.filter(item => item.visible !== false)

  if (visibleItems.length === 0) {
    return null
  }

  const triggerSizes = {
    sm: "h-6 w-6",
    md: "h-8 w-8", 
    lg: "h-10 w-10",
  }

  const contentSizes = {
    sm: "sm" as const,
    md: "md" as const,
    lg: "lg" as const,
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          ref={ref}
          variant={variant === "ghost" ? "ghost" : "outline"}
          size="icon"
          className={cn(
            triggerSizes[size],
            "text-muted-foreground hover:text-foreground",
            triggerClassName
          )}
          disabled={disabled}
          {...props}
        >
          {icon || <MoreVertical className="h-4 w-4" />}
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side={side}
        align={align}
        sideOffset={sideOffset}
        size={contentSizes[size]}
        className={cn("min-w-[160px]", contentClassName)}
      >
        {visibleItems.map((item, index) => {
          const isLast = index === visibleItems.length - 1

          return (
            <React.Fragment key={`${item.label}-${index}`}>
              <DropdownMenuItem
                onClick={item.onClick}
                disabled={item.disabled}
                variant={item.variant}
                size={size === "sm" ? "sm" : "md"}
                className={cn(
                  "cursor-pointer",
                  item.className
                )}
                asChild={!!item.href}
              >
                {item.href ? (
                  <a href={item.href} className="flex items-center gap-2">
                    {item.icon && (
                      <span className="flex h-4 w-4 items-center justify-center">
                        {item.icon}
                      </span>
                    )}
                    {item.label}
                  </a>
                ) : (
                  <>
                    {item.icon && (
                      <span className="flex h-4 w-4 items-center justify-center">
                        {item.icon}
                      </span>
                    )}
                    {item.label}
                  </>
                )}
              </DropdownMenuItem>
              {(item.separator || (!isLast && visibleItems[index + 1]?.separator)) && (
                <DropdownMenuSeparator />
              )}
            </React.Fragment>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
})
KebabMenu.displayName = "KebabMenu"

export { KebabMenu, kebabMenuVariants }
