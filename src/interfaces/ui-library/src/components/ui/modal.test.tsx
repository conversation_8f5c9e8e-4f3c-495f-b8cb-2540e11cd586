import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'

import { Modal, ConfirmModal } from './modal'

describe('Modal', () => {
  it('renders when open is true', () => {
    render(
      <Modal
        open={true}
        title="Test Modal"
        description="Test description"
      >
        <p>Modal content</p>
      </Modal>
    )

    expect(screen.getByText('Test Modal')).toBeInTheDocument()
    expect(screen.getByText('Test description')).toBeInTheDocument()
    expect(screen.getByText('Modal content')).toBeInTheDocument()
  })

  it('does not render when open is false', () => {
    render(
      <Modal
        open={false}
        title="Test Modal"
      >
        <p>Modal content</p>
      </Modal>
    )

    expect(screen.queryByText('Test Modal')).not.toBeInTheDocument()
    expect(screen.queryByText('Modal content')).not.toBeInTheDocument()
  })

  it('calls onOpenChange when close button is clicked', async () => {
    const user = userEvent.setup()
    const onOpenChange = vi.fn()
    
    render(
      <Modal
        open={true}
        onOpenChange={onOpenChange}
        title="Test Modal"
      >
        <p>Modal content</p>
      </Modal>
    )

    const closeButton = screen.getByRole('button', { name: /close/i })
    await user.click(closeButton)
    
    expect(onOpenChange).toHaveBeenCalledWith(false)
  })

  it('renders footer content', () => {
    render(
      <Modal
        open={true}
        title="Test Modal"
        footer={<button>Footer Button</button>}
      >
        <p>Modal content</p>
      </Modal>
    )

    expect(screen.getByText('Footer Button')).toBeInTheDocument()
  })

  it('renders custom header', () => {
    render(
      <Modal
        open={true}
        header={<div>Custom Header</div>}
      >
        <p>Modal content</p>
      </Modal>
    )

    expect(screen.getByText('Custom Header')).toBeInTheDocument()
  })

  it('applies spacing variants correctly', () => {
    render(
      <Modal
        open={true}
        spacing="lg"
        title="Spaced Modal"
      >
        <p>Content</p>
      </Modal>
    )

    const dialog = screen.getByRole('dialog')
    expect(dialog).toHaveClass('gap-6')
  })

  it('hides close button when showCloseButton is false', () => {
    render(
      <Modal
        open={true}
        showCloseButton={false}
        title="No Close Button"
      >
        <p>Content</p>
      </Modal>
    )

    expect(screen.queryByRole('button', { name: /close/i })).not.toBeInTheDocument()
  })

  it('passes through dialog props', () => {
    render(
      <Modal
        open={true}
        size="lg"
        position="top"
        title="Large Top Modal"
      >
        <p>Content</p>
      </Modal>
    )

    const dialog = screen.getByRole('dialog')
    expect(dialog).toHaveClass('max-w-2xl') // lg size
    expect(dialog).toHaveClass('top-[10%]') // top position
  })

  it('applies custom className', () => {
    render(
      <Modal
        open={true}
        className="custom-modal-class"
        title="Custom Modal"
      >
        <p>Content</p>
      </Modal>
    )

    const dialog = screen.getByRole('dialog')
    expect(dialog).toHaveClass('custom-modal-class')
  })

  it('applies container className to content area', () => {
    render(
      <Modal
        open={true}
        containerClassName="custom-container-class"
        title="Container Modal"
      >
        <p>Content</p>
      </Modal>
    )

    const contentContainer = screen.getByText('Content').parentElement
    expect(contentContainer).toHaveClass('custom-container-class')
  })

  it('renders without title and description', () => {
    render(
      <Modal open={true}>
        <p>Just content</p>
      </Modal>
    )

    expect(screen.getByText('Just content')).toBeInTheDocument()
    // Should not render header section
    expect(screen.queryByRole('heading')).not.toBeInTheDocument()
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    
    render(
      <Modal
        ref={ref}
        open={true}
        title="Ref Modal"
      >
        <p>Content</p>
      </Modal>
    )
    
    expect(ref).toHaveBeenCalled()
  })
})

describe('ConfirmModal', () => {
  it('renders with default confirm and cancel text', () => {
    render(
      <ConfirmModal
        open={true}
        title="Confirm Action"
        description="Are you sure?"
      />
    )

    expect(screen.getByText('Confirm')).toBeInTheDocument()
    expect(screen.getByText('Cancel')).toBeInTheDocument()
  })

  it('renders with custom button text', () => {
    render(
      <ConfirmModal
        open={true}
        title="Delete Item"
        confirmText="Delete"
        cancelText="Keep"
      />
    )

    expect(screen.getByText('Delete')).toBeInTheDocument()
    expect(screen.getByText('Keep')).toBeInTheDocument()
  })

  it('calls onConfirm when confirm button is clicked', async () => {
    const user = userEvent.setup()
    const onConfirm = vi.fn()
    const onOpenChange = vi.fn()
    
    render(
      <ConfirmModal
        open={true}
        onConfirm={onConfirm}
        onOpenChange={onOpenChange}
        title="Confirm Action"
      />
    )

    await user.click(screen.getByText('Confirm'))
    
    expect(onConfirm).toHaveBeenCalled()
    expect(onOpenChange).toHaveBeenCalledWith(false)
  })

  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup()
    const onCancel = vi.fn()
    const onOpenChange = vi.fn()
    
    render(
      <ConfirmModal
        open={true}
        onCancel={onCancel}
        onOpenChange={onOpenChange}
        title="Confirm Action"
      />
    )

    await user.click(screen.getByText('Cancel'))
    
    expect(onCancel).toHaveBeenCalled()
    expect(onOpenChange).toHaveBeenCalledWith(false)
  })

  it('shows loading state', () => {
    render(
      <ConfirmModal
        open={true}
        loading={true}
        title="Processing"
      />
    )

    expect(screen.getByText('Loading...')).toBeInTheDocument()
    
    const confirmButton = screen.getByText('Loading...')
    expect(confirmButton).toBeDisabled()
    
    const cancelButton = screen.getByText('Cancel')
    expect(cancelButton).toBeDisabled()
  })

  it('does not close modal when loading and confirm is clicked', async () => {
    const user = userEvent.setup()
    const onOpenChange = vi.fn()
    
    render(
      <ConfirmModal
        open={true}
        loading={true}
        onOpenChange={onOpenChange}
        title="Processing"
      />
    )

    await user.click(screen.getByText('Loading...'))
    
    // Should not call onOpenChange when loading
    expect(onOpenChange).not.toHaveBeenCalled()
  })

  it('applies destructive variant styling', () => {
    render(
      <ConfirmModal
        open={true}
        confirmVariant="destructive"
        title="Delete Item"
      />
    )

    const confirmButton = screen.getByText('Confirm')
    expect(confirmButton).toHaveClass('bg-red-600')
  })

  it('forwards modal props correctly', () => {
    render(
      <ConfirmModal
        open={true}
        size="lg"
        position="top"
        title="Large Confirm Modal"
      />
    )

    const dialog = screen.getByRole('dialog')
    expect(dialog).toHaveClass('max-w-2xl') // lg size
    expect(dialog).toHaveClass('top-[10%]') // top position
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    
    render(
      <ConfirmModal
        ref={ref}
        open={true}
        title="Ref Confirm Modal"
      />
    )
    
    expect(ref).toHaveBeenCalled()
  })
})
