import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'

import { RadioGroup, RadioGroupItem } from './radio-group'

describe('RadioGroup', () => {
  it('renders correctly with default props', () => {
    render(
      <RadioGroup>
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    const radioGroup = screen.getByRole('radiogroup')
    expect(radioGroup).toBeInTheDocument()
    
    const radioButtons = screen.getAllByRole('radio')
    expect(radioButtons).toHaveLength(2)
  })

  it('handles selection correctly', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(
      <RadioGroup onValueChange={handleChange}>
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    const firstRadio = screen.getAllByRole('radio')[0]
    await user.click(firstRadio)
    
    expect(handleChange).toHaveBeenCalledWith('option1')
  })

  it('allows only one selection at a time', async () => {
    const user = userEvent.setup()
    
    render(
      <RadioGroup>
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    const [firstRadio, secondRadio] = screen.getAllByRole('radio')
    
    await user.click(firstRadio)
    expect(firstRadio).toBeChecked()
    expect(secondRadio).not.toBeChecked()
    
    await user.click(secondRadio)
    expect(firstRadio).not.toBeChecked()
    expect(secondRadio).toBeChecked()
  })

  it('renders with default value', () => {
    render(
      <RadioGroup defaultValue="option2">
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    const [firstRadio, secondRadio] = screen.getAllByRole('radio')
    expect(firstRadio).not.toBeChecked()
    expect(secondRadio).toBeChecked()
  })

  it('can be disabled', () => {
    render(
      <RadioGroup disabled>
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    const radioButtons = screen.getAllByRole('radio')
    radioButtons.forEach(radio => {
      expect(radio).toBeDisabled()
    })
  })

  it('renders with different orientations', () => {
    const { rerender } = render(
      <RadioGroup orientation="vertical">
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    let radioGroup = screen.getByRole('radiogroup')
    expect(radioGroup).toHaveClass('grid-cols-1')
    
    rerender(
      <RadioGroup orientation="horizontal">
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    radioGroup = screen.getByRole('radiogroup')
    expect(radioGroup).toHaveClass('grid-flow-col')
  })

  it('applies custom className to RadioGroup', () => {
    render(
      <RadioGroup className="custom-group-class">
        <RadioGroupItem value="option1" />
      </RadioGroup>
    )
    
    const radioGroup = screen.getByRole('radiogroup')
    expect(radioGroup).toHaveClass('custom-group-class')
  })

  it('applies custom className to RadioGroupItem', () => {
    render(
      <RadioGroup>
        <RadioGroupItem value="option1" className="custom-item-class" />
      </RadioGroup>
    )
    
    const radioButton = screen.getByRole('radio')
    expect(radioButton).toHaveClass('custom-item-class')
  })

  it('forwards ref correctly for RadioGroup', () => {
    const ref = vi.fn()
    render(
      <RadioGroup ref={ref}>
        <RadioGroupItem value="option1" />
      </RadioGroup>
    )
    expect(ref).toHaveBeenCalled()
  })

  it('forwards ref correctly for RadioGroupItem', () => {
    const ref = vi.fn()
    render(
      <RadioGroup>
        <RadioGroupItem value="option1" ref={ref} />
      </RadioGroup>
    )
    expect(ref).toHaveBeenCalled()
  })

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup()
    
    render(
      <RadioGroup>
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
        <RadioGroupItem value="option3" />
      </RadioGroup>
    )
    
    const [firstRadio, secondRadio, thirdRadio] = screen.getAllByRole('radio')
    
    // Focus first radio
    firstRadio.focus()
    expect(firstRadio).toHaveFocus()
    
    // Arrow down should move to next radio
    await user.keyboard('{ArrowDown}')
    expect(secondRadio).toHaveFocus()

    // Arrow down again
    await user.keyboard('{ArrowDown}')
    expect(thirdRadio).toHaveFocus()

    // Arrow up should move back
    await user.keyboard('{ArrowUp}')
    expect(secondRadio).toHaveFocus()
  })

  it('renders with different sizes', () => {
    const { rerender } = render(
      <RadioGroup>
        <RadioGroupItem value="option1" size="sm" />
      </RadioGroup>
    )
    
    let radioButton = screen.getByRole('radio')
    expect(radioButton).toHaveClass('h-3.5', 'w-3.5')
    
    rerender(
      <RadioGroup>
        <RadioGroupItem value="option1" size="md" />
      </RadioGroup>
    )
    
    radioButton = screen.getByRole('radio')
    expect(radioButton).toHaveClass('h-4', 'w-4')
    
    rerender(
      <RadioGroup>
        <RadioGroupItem value="option1" size="lg" />
      </RadioGroup>
    )
    
    radioButton = screen.getByRole('radio')
    expect(radioButton).toHaveClass('h-5', 'w-5')
  })

  it('renders with different variants', () => {
    const { rerender } = render(
      <RadioGroup>
        <RadioGroupItem value="option1" variant="default" />
      </RadioGroup>
    )
    
    let radioButton = screen.getByRole('radio')
    expect(radioButton).toHaveClass('border-coral-500')
    
    rerender(
      <RadioGroup>
        <RadioGroupItem value="option1" variant="secondary" />
      </RadioGroup>
    )
    
    radioButton = screen.getByRole('radio')
    expect(radioButton).toHaveClass('border-secondary')
  })

  it('handles controlled state correctly', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    const { rerender } = render(
      <RadioGroup value="option1" onValueChange={handleChange}>
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    const [firstRadio, secondRadio] = screen.getAllByRole('radio')
    expect(firstRadio).toBeChecked()
    expect(secondRadio).not.toBeChecked()
    
    await user.click(secondRadio)
    expect(handleChange).toHaveBeenCalledWith('option2')
    
    // Simulate parent component updating the state
    rerender(
      <RadioGroup value="option2" onValueChange={handleChange}>
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    expect(firstRadio).not.toBeChecked()
    expect(secondRadio).toBeChecked()
  })

  it('works with form submission', () => {
    render(
      <form data-testid="test-form">
        <RadioGroup name="test-radio">
          <RadioGroupItem value="option1" />
          <RadioGroupItem value="option2" />
        </RadioGroup>
      </form>
    )

    const radioButtons = screen.getAllByRole('radio')
    // Radix components handle form submission internally
    expect(radioButtons).toHaveLength(2)
  })

  it('has proper accessibility attributes', () => {
    render(
      <RadioGroup aria-label="Test radio group">
        <RadioGroupItem value="option1" aria-label="Option 1" />
        <RadioGroupItem value="option2" aria-label="Option 2" />
      </RadioGroup>
    )
    
    const radioGroup = screen.getByRole('radiogroup')
    expect(radioGroup).toHaveAttribute('aria-label', 'Test radio group')
    
    const radioButtons = screen.getAllByRole('radio')
    expect(radioButtons[0]).toHaveAttribute('aria-label', 'Option 1')
    expect(radioButtons[1]).toHaveAttribute('aria-label', 'Option 2')
  })

  it('shows indicator when selected', () => {
    const { container } = render(
      <RadioGroup defaultValue="option1">
        <RadioGroupItem value="option1" />
        <RadioGroupItem value="option2" />
      </RadioGroup>
    )
    
    const indicators = container.querySelectorAll('svg')
    expect(indicators).toHaveLength(1) // Only selected item should have indicator
  })

  it('applies correct icon sizes for different radio sizes', () => {
    const { container: smallContainer } = render(
      <RadioGroup defaultValue="option1">
        <RadioGroupItem value="option1" size="sm" />
      </RadioGroup>
    )
    const smallIcon = smallContainer.querySelector('svg')
    expect(smallIcon).toHaveClass('h-1.5', 'w-1.5')

    const { container: mediumContainer } = render(
      <RadioGroup defaultValue="option1">
        <RadioGroupItem value="option1" size="md" />
      </RadioGroup>
    )
    const mediumIcon = mediumContainer.querySelector('svg')
    expect(mediumIcon).toHaveClass('h-2.5', 'w-2.5')

    const { container: largeContainer } = render(
      <RadioGroup defaultValue="option1">
        <RadioGroupItem value="option1" size="lg" />
      </RadioGroup>
    )
    const largeIcon = largeContainer.querySelector('svg')
    expect(largeIcon).toHaveClass('h-3', 'w-3')
  })
})
