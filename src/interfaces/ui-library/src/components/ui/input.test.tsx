import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import userEvent from '@testing-library/user-event'

import { Input } from './input'

// Mock clipboard API
const mockWriteText = vi.fn(() => Promise.resolve())
Object.assign(navigator, {
  clipboard: {
    writeText: mockWriteText,
  },
})

describe('Input', () => {
  it('renders correctly', () => {
    render(<Input placeholder="Test input" />)
    expect(screen.getByPlaceholderText('Test input')).toBeInTheDocument()
  })

  it('renders with label', () => {
    render(<Input label="Email" placeholder="Enter email" />)
    expect(screen.getByRole('textbox', { name: 'Email' })).toBeInTheDocument()
    expect(screen.getByText('Email')).toBeInTheDocument()
  })

  it('renders with description', () => {
    render(<Input label="Username" description="This will be public" />)
    expect(screen.getByText('This will be public')).toBeInTheDocument()
  })

  it('renders with error state', () => {
    render(<Input label="Password" hasError errorText="Password is required" />)
    expect(screen.getByText('Password is required')).toBeInTheDocument()
    expect(screen.getByRole('textbox', { name: 'Password' })).toHaveClass('border-danger-500')
  })

  it('handles value changes', async () => {
    const user = userEvent.setup()
    const handleChange = vi.fn()
    
    render(<Input onChange={handleChange} />)
    const input = screen.getByRole('textbox')
    
    await user.type(input, 'test')
    expect(handleChange).toHaveBeenCalledTimes(4) // Once for each character
  })

  it('renders different sizes', () => {
    const { rerender } = render(<Input size="sm" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveClass('h-9')

    rerender(<Input size="md" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveClass('h-10')

    rerender(<Input size="lg" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveClass('h-11')
  })

  it('renders different variants', () => {
    const { rerender } = render(<Input variant="default" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveClass('rounded-md')

    rerender(<Input variant="cell" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveClass('rounded-lg')
  })

  it('handles disabled state', () => {
    render(<Input disabled />)
    expect(screen.getByRole('textbox')).toBeDisabled()
  })

  it('handles loading state', () => {
    render(<Input isLoading />)
    expect(screen.getByRole('textbox')).toBeDisabled()
  })

  describe('Action Types', () => {
    it('renders copy action and handles copy', async () => {
      const user = userEvent.setup()
      const handleCopy = vi.fn()

      render(<Input actionType="copy" value="test text" onCopy={handleCopy} readOnly />)

      const copyButton = screen.getByRole('button')
      await user.click(copyButton)

      expect(mockWriteText).toHaveBeenCalledWith('test text')
      expect(handleCopy).toHaveBeenCalled()
    })

    it('renders reveal action for password', async () => {
      const user = userEvent.setup()
      
      render(<Input type="password" actionType="reveal" value="secret" />)
      
      const input = screen.getByDisplayValue('secret')
      expect(input).toHaveAttribute('type', 'password')
      
      const revealButton = screen.getByRole('button')
      await user.click(revealButton)
      
      expect(input).toHaveAttribute('type', 'text')
    })

    it('renders search action and handles search', async () => {
      const user = userEvent.setup()
      const handleSearch = vi.fn()
      
      render(<Input actionType="search" value="query" onSearch={handleSearch} readOnly />)
      
      const searchButton = screen.getByRole('button')
      await user.click(searchButton)
      
      expect(handleSearch).toHaveBeenCalledWith('query')
    })

    it('renders loading action with spinner', () => {
      render(<Input actionType="loading" />)
      expect(document.querySelector('.animate-spin')).toBeInTheDocument()
    })

    it('renders success action with check icon', () => {
      render(<Input actionType="success" />)
      expect(screen.getByRole('textbox').parentElement).toContainElement(
        document.querySelector('svg')
      )
    })
  })

  it('shows copy success state temporarily', async () => {
    const user = userEvent.setup()

    render(<Input actionType="copy" value="test" readOnly />)

    const copyButton = screen.getByRole('button')

    // Should show copy icon initially
    expect(copyButton.querySelector('svg')).toBeInTheDocument()

    await user.click(copyButton)

    // Should show check icon after copy
    await waitFor(() => {
      expect(copyButton.querySelector('svg')).toBeInTheDocument()
    })
  })

  it('disables action buttons when input is disabled', () => {
    render(<Input actionType="copy" value="test" disabled />)
    expect(screen.getByRole('button')).toBeDisabled()
  })

  it('disables copy button when no value', () => {
    render(<Input actionType="copy" value="" readOnly />)
    expect(screen.getByRole('button')).toBeDisabled()
  })

  it('disables search button when no value', () => {
    render(<Input actionType="search" value="" readOnly />)
    expect(screen.getByRole('button')).toBeDisabled()
  })

  it('applies custom className', () => {
    render(<Input className="custom-class" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveClass('custom-class')
  })

  it('applies custom containerClassName', () => {
    render(<Input containerClassName="custom-container" />)
    expect(screen.getByRole('textbox').closest('div')?.parentElement).toHaveClass('custom-container')
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(<Input ref={ref} />)
    expect(ref).toHaveBeenCalled()
  })

  it('handles different input types', () => {
    const { rerender } = render(<Input type="email" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveAttribute('type', 'email')

    rerender(<Input type="number" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveAttribute('type', 'number')

    rerender(<Input type="tel" data-testid="input" />)
    expect(screen.getByTestId('input')).toHaveAttribute('type', 'tel')
  })

  it('shows error text instead of description when error', () => {
    render(
      <Input 
        description="Normal description" 
        hasError 
        errorText="Error message" 
      />
    )
    
    expect(screen.getByText('Error message')).toBeInTheDocument()
    expect(screen.queryByText('Normal description')).not.toBeInTheDocument()
  })

  it('handles copy error gracefully', async () => {
    const user = userEvent.setup()
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    // Mock clipboard to throw error
    mockWriteText.mockRejectedValueOnce(new Error('Copy failed'))

    render(<Input actionType="copy" value="test" readOnly />)

    const copyButton = screen.getByRole('button')
    await user.click(copyButton)

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to copy:', expect.any(Error))
    })

    consoleSpy.mockRestore()
    mockWriteText.mockResolvedValue(undefined) // Reset mock
  })
})
