import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Heart, Download, ArrowR<PERSON>, Plus } from 'lucide-react'

import { Button } from './button'

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link', 'cell'],
    },
    size: {
      control: { type: 'select' },
      options: ['default', 'sm', 'lg', 'icon'],
    },
    theme: {
      control: { type: 'select' },
      options: [
        'default',
        'coral',
        'evolved-green',
        'blue',
        'evolved-blue',
        'green',
        'quartz',
        'evolved-quartz',
        'mushroom',
        'evolved-mushroom',
        'danger',
      ],
    },
    iconPosition: {
      control: { type: 'select' },
      options: ['start', 'end'],
    },
    isLoading: {
      control: { type: 'boolean' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: 'Button',
  },
}

export const Variants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="default">Default</Button>
      <Button variant="destructive">Destructive</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="link">Link</Button>
    </div>
  ),
}

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="sm">Small</Button>
      <Button size="default">Default</Button>
      <Button size="lg">Large</Button>
      <Button size="icon">
        <Heart className="h-4 w-4" />
      </Button>
    </div>
  ),
}

export const Themes: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button theme="default">Default</Button>
      <Button theme="coral">Coral</Button>
      <Button theme="evolved-green">Evolved Green</Button>
      <Button theme="blue">Blue</Button>
      <Button theme="evolved-blue">Evolved Blue</Button>
      <Button theme="green">Green</Button>
      <Button theme="quartz">Quartz</Button>
      <Button theme="evolved-quartz">Evolved Quartz</Button>
      <Button theme="mushroom">Mushroom</Button>
      <Button theme="evolved-mushroom">Evolved Mushroom</Button>
      <Button theme="danger">Danger</Button>
    </div>
  ),
}

export const WithIcons: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button icon={<Download className="h-4 w-4" />} iconPosition="start">
        Download
      </Button>
      <Button icon={<ArrowRight className="h-4 w-4" />} iconPosition="end">
        Continue
      </Button>
      <Button size="icon" icon={<Plus className="h-4 w-4" />} />
    </div>
  ),
}

export const Loading: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button isLoading>Loading</Button>
      <Button isLoading loadingText="Saving...">
        Save
      </Button>
      <Button isLoading variant="outline">
        Processing
      </Button>
    </div>
  ),
}

export const AsLink: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button href="https://example.com" target="_blank" rel="noopener noreferrer">
        External Link
      </Button>
      <Button href="/dashboard" variant="outline">
        Internal Link
      </Button>
    </div>
  ),
}

export const States: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button>Normal</Button>
      <Button disabled>Disabled</Button>
      <Button isLoading>Loading</Button>
      <Button variant="destructive" disabled>
        Disabled Destructive
      </Button>
    </div>
  ),
}

export const Interactive: Story = {
  args: {
    children: 'Click me',
    variant: 'default',
    size: 'default',
    theme: 'coral',
  },
}
