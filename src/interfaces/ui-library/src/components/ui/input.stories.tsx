import type { <PERSON>a, StoryObj } from '@storybook/react'
import { useState } from 'react'

import { Input } from './input'

const meta: Meta<typeof Input> = {
  title: 'UI/Input',
  component: Input,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    actionType: {
      control: { type: 'select' },
      options: ['copy', 'reveal', 'search', 'loading', 'success'],
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
    },
    variant: {
      control: { type: 'select' },
      options: ['default', 'cell'],
    },
    type: {
      control: { type: 'select' },
      options: ['text', 'email', 'password', 'number', 'tel', 'url'],
    },
    hasError: {
      control: { type: 'boolean' },
    },
    isLoading: {
      control: { type: 'boolean' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    placeholder: 'Enter text...',
  },
}

export const WithLabel: Story = {
  args: {
    label: 'Email Address',
    placeholder: '<EMAIL>',
    type: 'email',
  },
}

export const WithDescription: Story = {
  args: {
    label: 'Username',
    description: 'This will be your public display name.',
    placeholder: 'Enter username...',
  },
}

export const WithError: Story = {
  args: {
    label: 'Password',
    type: 'password',
    hasError: true,
    errorText: 'Password must be at least 8 characters long.',
    value: '123',
  },
}

export const Sizes: Story = {
  render: () => (
    <div className="flex flex-col gap-4 w-80">
      <Input size="sm" placeholder="Small input" label="Small" />
      <Input size="md" placeholder="Medium input" label="Medium" />
      <Input size="lg" placeholder="Large input" label="Large" />
    </div>
  ),
}

export const Variants: Story = {
  render: () => (
    <div className="flex flex-col gap-4 w-80">
      <Input variant="default" placeholder="Default variant" label="Default" />
      <Input variant="cell" placeholder="Cell variant" label="Cell" />
    </div>
  ),
}

export const ActionTypes: Story = {
  render: () => {
    const [copyValue, setCopyValue] = useState('Text to copy')
    const [searchValue, setSearchValue] = useState('Search query')
    
    return (
      <div className="flex flex-col gap-4 w-80">
        <Input
          actionType="copy"
          label="Copy Action"
          value={copyValue}
          onChange={(e) => setCopyValue(e.target.value)}
          onCopy={() => console.log('Copied!')}
        />
        
        <Input
          actionType="reveal"
          type="password"
          label="Password with Reveal"
          placeholder="Enter password..."
        />
        
        <Input
          actionType="search"
          label="Search Input"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onSearch={(value) => console.log('Searching for:', value)}
        />
        
        <Input
          actionType="loading"
          label="Loading State"
          placeholder="Processing..."
        />
        
        <Input
          actionType="success"
          label="Success State"
          value="Operation completed"
          readOnly
        />
      </div>
    )
  },
}

export const PasswordInput: Story = {
  render: () => {
    const [password, setPassword] = useState('')
    
    return (
      <Input
        type="password"
        actionType="reveal"
        label="Password"
        placeholder="Enter your password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
    )
  },
}

export const SearchInput: Story = {
  render: () => {
    const [query, setQuery] = useState('')
    
    return (
      <Input
        actionType="search"
        label="Search"
        placeholder="Search for anything..."
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onSearch={(value) => alert(`Searching for: ${value}`)}
      />
    )
  },
}

export const CopyInput: Story = {
  render: () => {
    const [text, setText] = useState('https://example.com/api/key/abc123')
    
    return (
      <Input
        actionType="copy"
        label="API Key"
        description="Click the copy button to copy to clipboard"
        value={text}
        onChange={(e) => setText(e.target.value)}
        onCopy={() => alert('Copied to clipboard!')}
        readOnly
      />
    )
  },
}

export const FormExample: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    })
    
    const [errors, setErrors] = useState<Record<string, string>>({})
    
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      const newErrors: Record<string, string> = {}
      
      if (!formData.name) newErrors.name = 'Name is required'
      if (!formData.email) newErrors.email = 'Email is required'
      if (!formData.password) newErrors.password = 'Password is required'
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match'
      }
      
      setErrors(newErrors)
      
      if (Object.keys(newErrors).length === 0) {
        alert('Form submitted successfully!')
      }
    }
    
    return (
      <form onSubmit={handleSubmit} className="flex flex-col gap-4 w-80">
        <Input
          label="Full Name"
          placeholder="Enter your name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          hasError={!!errors.name}
          errorText={errors.name}
        />
        
        <Input
          type="email"
          label="Email"
          placeholder="<EMAIL>"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          hasError={!!errors.email}
          errorText={errors.email}
        />
        
        <Input
          type="password"
          actionType="reveal"
          label="Password"
          placeholder="Enter password"
          value={formData.password}
          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
          hasError={!!errors.password}
          errorText={errors.password}
        />
        
        <Input
          type="password"
          actionType="reveal"
          label="Confirm Password"
          placeholder="Confirm password"
          value={formData.confirmPassword}
          onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
          hasError={!!errors.confirmPassword}
          errorText={errors.confirmPassword}
        />
        
        <button
          type="submit"
          className="mt-4 px-4 py-2 bg-coral-500 text-white rounded-md hover:bg-coral-400"
        >
          Submit
        </button>
      </form>
    )
  },
}

export const States: Story = {
  render: () => (
    <div className="flex flex-col gap-4 w-80">
      <Input label="Normal" placeholder="Normal state" />
      <Input label="Disabled" placeholder="Disabled state" disabled />
      <Input label="Loading" placeholder="Loading state" isLoading />
      <Input label="Error" placeholder="Error state" hasError errorText="Something went wrong" />
      <Input label="Read Only" value="Read only value" readOnly />
    </div>
  ),
}

export const Interactive: Story = {
  args: {
    label: 'Interactive Input',
    placeholder: 'Type something...',
    size: 'md',
    variant: 'default',
  },
}
