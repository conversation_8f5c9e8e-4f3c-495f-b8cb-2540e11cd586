import type { <PERSON>a, StoryObj } from '@storybook/react';
import { IconButton } from './icon-button';
import { CommonIcons } from './icon';

const meta: Meta<typeof IconButton> = {
  title: 'UI/IconButton',
  component: IconButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    icon: {
      control: 'select',
      options: Object.values(CommonIcons),
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
    },
    variant: {
      control: 'select',
      options: ['default', 'outline', 'ghost', 'secondary'],
    },
    disabled: {
      control: 'boolean',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    icon: CommonIcons.Home,
    'aria-label': 'Home',
  },
};

export const Variants: Story = {
  render: () => (
    <div className="flex gap-4">
      <IconButton icon={CommonIcons.Home} variant="default" aria-label="Home default" />
      <IconButton icon={CommonIcons.Home} variant="outline" aria-label="Home outline" />
      <IconButton icon={CommonIcons.Home} variant="ghost" aria-label="Home ghost" />
      <IconButton icon={CommonIcons.Home} variant="secondary" aria-label="Home secondary" />
    </div>
  ),
};

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <div className="flex flex-col items-center gap-2">
        <IconButton icon={CommonIcons.Star} size="xs" aria-label="Star extra small" />
        <span className="text-xs">xs</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <IconButton icon={CommonIcons.Star} size="sm" aria-label="Star small" />
        <span className="text-xs">sm</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <IconButton icon={CommonIcons.Star} size="md" aria-label="Star medium" />
        <span className="text-xs">md</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <IconButton icon={CommonIcons.Star} size="lg" aria-label="Star large" />
        <span className="text-xs">lg</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <IconButton icon={CommonIcons.Star} size="xl" aria-label="Star extra large" />
        <span className="text-xs">xl</span>
      </div>
    </div>
  ),
};

export const CommonActions: Story = {
  render: () => (
    <div className="flex gap-2">
      <IconButton icon={CommonIcons.Plus} aria-label="Add" />
      <IconButton icon={CommonIcons.Edit} aria-label="Edit" />
      <IconButton icon={CommonIcons.Copy} aria-label="Copy" />
      <IconButton icon={CommonIcons.Trash} aria-label="Delete" />
      <IconButton icon={CommonIcons.Share} aria-label="Share" />
      <IconButton icon={CommonIcons.Download} aria-label="Download" />
      <IconButton icon={CommonIcons.Settings} aria-label="Settings" />
    </div>
  ),
};

export const Navigation: Story = {
  render: () => (
    <div className="flex gap-2">
      <IconButton icon={CommonIcons.ArrowLeft} aria-label="Previous" />
      <IconButton icon={CommonIcons.ArrowUp} aria-label="Up" />
      <IconButton icon={CommonIcons.ArrowDown} aria-label="Down" />
      <IconButton icon={CommonIcons.ArrowRight} aria-label="Next" />
    </div>
  ),
};

export const States: Story = {
  render: () => (
    <div className="flex gap-4">
      <div className="flex flex-col items-center gap-2">
        <IconButton icon={CommonIcons.Home} aria-label="Normal state" />
        <span className="text-xs">Normal</span>
      </div>
      <div className="flex flex-col items-center gap-2">
        <IconButton icon={CommonIcons.Home} disabled aria-label="Disabled state" />
        <span className="text-xs">Disabled</span>
      </div>
    </div>
  ),
};

export const WithTooltip: Story = {
  render: () => (
    <div className="flex gap-4">
      <IconButton 
        icon={CommonIcons.Heart} 
        aria-label="Like this item"
        title="Like this item"
      />
      <IconButton 
        icon={CommonIcons.Bookmark} 
        aria-label="Bookmark this page"
        title="Bookmark this page"
      />
      <IconButton 
        icon={CommonIcons.Bell} 
        aria-label="View notifications"
        title="View notifications"
      />
    </div>
  ),
};

export const Loading: Story = {
  render: () => (
    <div className="flex gap-4">
      <IconButton 
        icon={CommonIcons.Loader} 
        aria-label="Loading"
        className="[&>svg]:animate-spin"
      />
      <IconButton 
        icon={CommonIcons.RefreshCw} 
        aria-label="Refresh"
        className="[&>svg]:animate-spin"
      />
    </div>
  ),
};

export const Interactive: Story = {
  render: () => (
    <div className="flex gap-4">
      <IconButton 
        icon={CommonIcons.ThumbsUp} 
        aria-label="Like"
        onClick={() => alert('Liked!')}
        className="hover:text-green-500"
      />
      <IconButton 
        icon={CommonIcons.ThumbsDown} 
        aria-label="Dislike"
        onClick={() => alert('Disliked!')}
        className="hover:text-red-500"
      />
      <IconButton 
        icon={CommonIcons.Star} 
        aria-label="Favorite"
        onClick={() => alert('Favorited!')}
        className="hover:text-yellow-500"
      />
    </div>
  ),
};

export const CustomIconSize: Story = {
  render: () => (
    <div className="flex gap-4">
      <IconButton 
        icon={CommonIcons.Search} 
        size="lg"
        iconSize="sm"
        aria-label="Search with small icon"
      />
      <IconButton 
        icon={CommonIcons.Search} 
        size="sm"
        iconSize="lg"
        aria-label="Search with large icon"
      />
    </div>
  ),
};

export const AsLink: Story = {
  render: () => (
    <div className="flex gap-4">
      <IconButton 
        icon={CommonIcons.ExternalLink} 
        aria-label="Open external link"
        asChild
      >
        <a href="https://example.com" target="_blank" rel="noopener noreferrer">
        </a>
      </IconButton>
      <IconButton 
        icon={CommonIcons.Github} 
        aria-label="View on GitHub"
        asChild
      >
        <a href="https://github.com" target="_blank" rel="noopener noreferrer">
        </a>
      </IconButton>
    </div>
  ),
};
