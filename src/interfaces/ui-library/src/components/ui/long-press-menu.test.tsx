import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { LongPressMenu, LongPressMenuItem } from './long-press-menu'

describe('LongPressMenu', () => {
  const mockItems = [
    {
      label: 'Edit',
      onClick: vi.fn(),
    },
    {
      label: 'Copy',
      onClick: vi.fn(),
    },
    {
      label: 'Delete',
      onClick: vi.fn(),
      variant: 'destructive' as const,
    },
  ]

  const mockOnClose = vi.fn()

  beforeEach(() => {
    mockItems.forEach(item => item.onClick.mockClear())
    mockOnClose.mockClear()
  })

  it('renders when open', () => {
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
      />
    )
    
    expect(screen.getByText('Edit')).toBeInTheDocument()
    expect(screen.getByText('Copy')).toBeInTheDocument()
    expect(screen.getByText('Delete')).toBeInTheDocument()
    expect(screen.getByText('Cancel')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(
      <LongPressMenu
        isOpen={false}
        onClose={mockOnClose}
        items={mockItems}
      />
    )
    
    expect(screen.queryByText('Edit')).not.toBeInTheDocument()
  })

  it('renders with title', () => {
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
        title="Choose an action"
      />
    )
    
    expect(screen.getByText('Choose an action')).toBeInTheDocument()
  })

  it('calls onClose when cancel is clicked', async () => {
    const user = userEvent.setup()
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
      />
    )
    
    await user.click(screen.getByText('Cancel'))
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('calls item onClick and closes menu', async () => {
    const user = userEvent.setup()
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
      />
    )
    
    await user.click(screen.getByText('Edit'))
    expect(mockItems[0].onClick).toHaveBeenCalledTimes(1)
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('renders with different sizes', () => {
    const { rerender } = render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
        size="sm"
      />
    )
    
    let content = screen.getByText('Edit').closest('[role="dialog"]')
    expect(content).toHaveClass('p-4', 'gap-2')
    
    rerender(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
        size="md"
      />
    )
    
    content = screen.getByText('Edit').closest('[role="dialog"]')
    expect(content).toHaveClass('p-6', 'gap-4')
    
    rerender(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
        size="lg"
      />
    )
    
    content = screen.getByText('Edit').closest('[role="dialog"]')
    expect(content).toHaveClass('p-8', 'gap-6')
  })

  it('handles disabled items', async () => {
    const itemsWithDisabled = [
      {
        label: 'Enabled',
        onClick: vi.fn(),
      },
      {
        label: 'Disabled',
        onClick: vi.fn(),
        disabled: true,
      },
    ]
    
    const user = userEvent.setup()
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={itemsWithDisabled}
      />
    )
    
    const disabledButton = screen.getByText('Disabled').closest('button')
    expect(disabledButton).toBeDisabled()
    
    // Try to click disabled button
    await user.click(disabledButton)
    expect(itemsWithDisabled[1].onClick).not.toHaveBeenCalled()
  })

  it('renders destructive variant items', () => {
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
      />
    )
    
    const deleteButton = screen.getByText('Delete').closest('button')
    expect(deleteButton).toHaveClass('text-destructive')
  })

  it('renders with icons', () => {
    const itemsWithIcons = [
      {
        label: 'Edit',
        icon: <span data-testid="edit-icon">✏️</span>,
        onClick: vi.fn(),
      },
    ]
    
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={itemsWithIcons}
      />
    )
    
    expect(screen.getByTestId('edit-icon')).toBeInTheDocument()
  })

  it('renders custom children instead of items', () => {
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
      >
        <div>Custom content</div>
      </LongPressMenu>
    )
    
    expect(screen.getByText('Custom content')).toBeInTheDocument()
    expect(screen.getByText('Cancel')).toBeInTheDocument()
  })

  it('closes when overlay is clicked', async () => {
    const user = userEvent.setup()
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
      />
    )
    
    // Click on the overlay (outside the dialog content)
    const overlay = document.querySelector('[data-state="open"]')
    if (overlay) {
      await user.click(overlay)
      expect(mockOnClose).toHaveBeenCalled()
    }
  })

  it('applies custom className', () => {
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
        className="custom-class"
      />
    )
    
    const content = screen.getByText('Edit').closest('[role="dialog"]')
    expect(content).toHaveClass('custom-class')
  })

  it('applies custom overlay className', () => {
    render(
      <LongPressMenu
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
        overlayClassName="custom-overlay"
      />
    )
    
    const overlay = document.querySelector('.custom-overlay')
    expect(overlay).toBeInTheDocument()
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(
      <LongPressMenu
        ref={ref}
        isOpen={true}
        onClose={mockOnClose}
        items={mockItems}
      />
    )
    
    expect(ref).toHaveBeenCalled()
  })
})

describe('LongPressMenuItem', () => {
  it('renders with icon and children', () => {
    render(
      <LongPressMenuItem icon={<span data-testid="icon">🔧</span>}>
        Settings
      </LongPressMenuItem>
    )
    
    expect(screen.getByTestId('icon')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()
  })

  it('renders with destructive variant', () => {
    render(
      <LongPressMenuItem variant="destructive">
        Delete
      </LongPressMenuItem>
    )
    
    const button = screen.getByText('Delete').closest('button')
    expect(button).toHaveClass('text-destructive')
  })

  it('handles click events', async () => {
    const onClick = vi.fn()
    const user = userEvent.setup()
    
    render(
      <LongPressMenuItem onClick={onClick}>
        Click me
      </LongPressMenuItem>
    )
    
    await user.click(screen.getByText('Click me'))
    expect(onClick).toHaveBeenCalledTimes(1)
  })

  it('applies custom className', () => {
    render(
      <LongPressMenuItem className="custom-item">
        Custom
      </LongPressMenuItem>
    )
    
    const button = screen.getByText('Custom').closest('button')
    expect(button).toHaveClass('custom-item')
  })

  it('forwards ref correctly', () => {
    const ref = vi.fn()
    render(
      <LongPressMenuItem ref={ref}>
        Item
      </LongPressMenuItem>
    )
    
    expect(ref).toHaveBeenCalled()
  })
})
