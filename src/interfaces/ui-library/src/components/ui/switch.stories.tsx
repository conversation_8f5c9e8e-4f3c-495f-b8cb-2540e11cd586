import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Switch } from './switch';
import { Label } from './label';

const meta: Meta<typeof Switch> = {
  title: 'UI/Switch',
  component: Switch,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    checked: {
      control: 'boolean',
    },
    disabled: {
      control: 'boolean',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
    variant: {
      control: 'select',
      options: ['default', 'secondary', 'success'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    checked: false,
    disabled: false,
    size: 'lg',
    variant: 'default',
  },
};

export const Checked: Story = {
  args: {
    checked: true,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};

export const DisabledChecked: Story = {
  args: {
    checked: true,
    disabled: true,
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-6">
      <div className="flex items-center gap-2">
        <Switch id="small" size="sm" />
        <Label htmlFor="small">Small</Label>
      </div>
      <div className="flex items-center gap-2">
        <Switch id="medium" size="md" />
        <Label htmlFor="medium">Medium</Label>
      </div>
      <div className="flex items-center gap-2">
        <Switch id="large" size="lg" />
        <Label htmlFor="large">Large</Label>
      </div>
    </div>
  ),
};

export const Variants: Story = {
  render: () => (
    <div className="flex items-center gap-6">
      <div className="flex items-center gap-2">
        <Switch id="default" variant="default" checked />
        <Label htmlFor="default">Default</Label>
      </div>
      <div className="flex items-center gap-2">
        <Switch id="secondary" variant="secondary" checked />
        <Label htmlFor="secondary">Secondary</Label>
      </div>
      <div className="flex items-center gap-2">
        <Switch id="success" variant="success" checked />
        <Label htmlFor="success">Success</Label>
      </div>
    </div>
  ),
};

export const WithLabel: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Switch id="airplane-mode" />
      <Label htmlFor="airplane-mode">Airplane Mode</Label>
    </div>
  ),
};

export const Interactive: Story = {
  render: () => {
    const [enabled, setEnabled] = useState(false);
    
    return (
      <div className="flex items-center space-x-2">
        <Switch 
          id="interactive" 
          checked={enabled}
          onCheckedChange={setEnabled}
        />
        <Label htmlFor="interactive">
          {enabled ? 'Enabled' : 'Disabled'}
        </Label>
      </div>
    );
  },
};

export const SettingsExample: Story = {
  render: () => {
    const [settings, setSettings] = useState({
      notifications: true,
      marketing: false,
      analytics: true,
      cookies: false,
    });

    const updateSetting = (key: keyof typeof settings) => (checked: boolean) => {
      setSettings(prev => ({ ...prev, [key]: checked }));
    };

    return (
      <div className="space-y-6">
        <h3 className="text-lg font-medium">Privacy Settings</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="notifications" className="text-base">
                Push Notifications
              </Label>
              <p className="text-sm text-muted-foreground">
                Receive notifications about your account activity.
              </p>
            </div>
            <Switch
              id="notifications"
              checked={settings.notifications}
              onCheckedChange={updateSetting('notifications')}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="marketing" className="text-base">
                Marketing Emails
              </Label>
              <p className="text-sm text-muted-foreground">
                Receive emails about new products and features.
              </p>
            </div>
            <Switch
              id="marketing"
              checked={settings.marketing}
              onCheckedChange={updateSetting('marketing')}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="analytics" className="text-base">
                Analytics
              </Label>
              <p className="text-sm text-muted-foreground">
                Help us improve by sharing usage data.
              </p>
            </div>
            <Switch
              id="analytics"
              checked={settings.analytics}
              onCheckedChange={updateSetting('analytics')}
              variant="success"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="cookies" className="text-base">
                Functional Cookies
              </Label>
              <p className="text-sm text-muted-foreground">
                Enable cookies for enhanced functionality.
              </p>
            </div>
            <Switch
              id="cookies"
              checked={settings.cookies}
              onCheckedChange={updateSetting('cookies')}
              variant="secondary"
            />
          </div>
        </div>
      </div>
    );
  },
};

export const CompactForm: Story = {
  render: () => {
    const [compactSettings, setCompactSettings] = useState({
      wifi: true,
      bluetooth: false,
      location: true,
    });

    return (
      <div className="space-y-3">
        <h3 className="text-base font-medium">Quick Settings</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="wifi" className="text-sm">Wi-Fi</Label>
            <Switch
              id="wifi"
              size="sm"
              checked={compactSettings.wifi}
              onCheckedChange={(checked) => 
                setCompactSettings(prev => ({ ...prev, wifi: checked }))
              }
            />
          </div>
          <div className="flex items-center justify-between">
            <Label htmlFor="bluetooth" className="text-sm">Bluetooth</Label>
            <Switch
              id="bluetooth"
              size="sm"
              checked={compactSettings.bluetooth}
              onCheckedChange={(checked) => 
                setCompactSettings(prev => ({ ...prev, bluetooth: checked }))
              }
            />
          </div>
          <div className="flex items-center justify-between">
            <Label htmlFor="location" className="text-sm">Location</Label>
            <Switch
              id="location"
              size="sm"
              checked={compactSettings.location}
              onCheckedChange={(checked) => 
                setCompactSettings(prev => ({ ...prev, location: checked }))
              }
            />
          </div>
        </div>
      </div>
    );
  },
};
