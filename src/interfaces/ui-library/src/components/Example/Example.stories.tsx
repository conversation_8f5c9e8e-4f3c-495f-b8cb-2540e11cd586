import type { Meta, StoryObj } from '@storybook/react';
import { Example } from './Example';

const meta: Meta<typeof Example> = {
  title: 'Example/Example',
  component: Example,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    children: {
      control: 'text',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Hello, Cohere UI Library! 🎉',
  },
};

export const WithCustomContent: Story = {
  args: {
    children: (
      <div>
        <h3 className="mb-2 text-lg font-semibold">Welcome to the UI Library</h3>
        <p className="text-sm opacity-80">
          This is an example component to test our Storybook setup with TailwindCSS and theme switching.
        </p>
      </div>
    ),
  },
};
