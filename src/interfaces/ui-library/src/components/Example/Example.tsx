import React from 'react';
import { cn } from '@/utils';

interface ExampleProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Example component to test the UI library setup
 */
export const Example: React.FC<ExampleProps> = ({ children, className }) => {
  return (
    <div
      className={cn(
        'rounded-lg border border-volcanic-800 bg-white p-4 text-volcanic-300',
        'dark:border-volcanic-500 dark:bg-volcanic-100 dark:text-white',
        className
      )}
    >
      {children}
    </div>
  );
};
