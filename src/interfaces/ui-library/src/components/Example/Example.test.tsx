import { describe, it, expect } from 'vitest';
import { render, screen } from '@/test/utils';
import { Example } from './Example';

describe('Example', () => {
  it('renders children correctly', () => {
    render(<Example>Hello World</Example>);
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<Example className="custom-class">Test</Example>);
    const element = screen.getByText('Test');
    expect(element).toHaveClass('custom-class');
  });

  it('has default styling classes', () => {
    render(<Example>Test</Example>);
    const element = screen.getByText('Test');
    expect(element).toHaveClass('rounded-lg', 'border', 'p-4');
  });

  it('renders complex children', () => {
    render(
      <Example>
        <div>
          <h1>Title</h1>
          <p>Description</p>
        </div>
      </Example>
    );
    
    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
  });
});
