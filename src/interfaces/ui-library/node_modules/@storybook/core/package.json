{"name": "@storybook/core", "version": "8.6.14", "description": "Storybook framework-agnostic API", "keywords": ["storybook"], "homepage": "https://storybook.js.org", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/core"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./node-logger": {"types": "./dist/node-logger/index.d.ts", "import": "./dist/node-logger/index.js", "require": "./dist/node-logger/index.cjs"}, "./client-logger": {"types": "./dist/client-logger/index.d.ts", "import": "./dist/client-logger/index.js", "require": "./dist/client-logger/index.cjs"}, "./theming": {"types": "./dist/theming/index.d.ts", "import": "./dist/theming/index.js", "require": "./dist/theming/index.cjs"}, "./theming/create": {"types": "./dist/theming/create.d.ts", "import": "./dist/theming/create.js", "require": "./dist/theming/create.cjs"}, "./core-server": {"types": "./dist/core-server/index.d.ts", "import": "./dist/core-server/index.js", "require": "./dist/core-server/index.cjs"}, "./core-server/presets/common-preset": {"import": "./dist/core-server/presets/common-preset.js", "require": "./dist/core-server/presets/common-preset.cjs"}, "./core-server/presets/common-manager": {"import": "./dist/core-server/presets/common-manager.js"}, "./core-server/presets/common-override-preset": {"import": "./dist/core-server/presets/common-override-preset.js", "require": "./dist/core-server/presets/common-override-preset.cjs"}, "./core-events": {"types": "./dist/core-events/index.d.ts", "import": "./dist/core-events/index.js", "require": "./dist/core-events/index.cjs"}, "./manager-errors": {"types": "./dist/manager-errors.d.ts", "import": "./dist/manager-errors.js"}, "./preview-errors": {"types": "./dist/preview-errors.d.ts", "import": "./dist/preview-errors.js", "require": "./dist/preview-errors.cjs"}, "./server-errors": {"types": "./dist/server-errors.d.ts", "import": "./dist/server-errors.js", "require": "./dist/server-errors.cjs"}, "./channels": {"types": "./dist/channels/index.d.ts", "import": "./dist/channels/index.js", "require": "./dist/channels/index.cjs"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js", "require": "./dist/types/index.cjs"}, "./csf-tools": {"types": "./dist/csf-tools/index.d.ts", "import": "./dist/csf-tools/index.js", "require": "./dist/csf-tools/index.cjs"}, "./csf": {"types": "./dist/csf/index.d.ts", "import": "./dist/csf/index.js", "require": "./dist/csf/index.cjs"}, "./common": {"types": "./dist/common/index.d.ts", "import": "./dist/common/index.js", "require": "./dist/common/index.cjs"}, "./builder-manager": {"types": "./dist/builder-manager/index.d.ts", "import": "./dist/builder-manager/index.js", "require": "./dist/builder-manager/index.cjs"}, "./telemetry": {"types": "./dist/telemetry/index.d.ts", "import": "./dist/telemetry/index.js", "require": "./dist/telemetry/index.cjs"}, "./preview-api": {"types": "./dist/preview-api/index.d.ts", "import": "./dist/preview-api/index.js", "require": "./dist/preview-api/index.cjs"}, "./manager-api": {"types": "./dist/manager-api/index.d.ts", "import": "./dist/manager-api/index.js", "require": "./dist/manager-api/index.cjs"}, "./router": {"types": "./dist/router/index.d.ts", "import": "./dist/router/index.js", "require": "./dist/router/index.cjs"}, "./components": {"types": "./dist/components/index.d.ts", "import": "./dist/components/index.js", "require": "./dist/components/index.cjs"}, "./docs-tools": {"types": "./dist/docs-tools/index.d.ts", "import": "./dist/docs-tools/index.js", "require": "./dist/docs-tools/index.cjs"}, "./manager/globals-module-info": {"types": "./dist/manager/globals-module-info.d.ts", "import": "./dist/manager/globals-module-info.js", "require": "./dist/manager/globals-module-info.cjs"}, "./manager/globals": {"types": "./dist/manager/globals.d.ts", "import": "./dist/manager/globals.js", "require": "./dist/manager/globals.cjs"}, "./preview/globals": {"types": "./dist/preview/globals.d.ts", "import": "./dist/preview/globals.js", "require": "./dist/preview/globals.cjs"}, "./cli": {"types": "./dist/cli/index.d.ts", "import": "./dist/cli/index.js", "require": "./dist/cli/index.cjs"}, "./babel": {"types": "./dist/babel/index.d.ts", "import": "./dist/babel/index.js", "require": "./dist/babel/index.cjs"}, "./cli/bin": {"types": "./dist/cli/bin/index.d.ts", "import": "./dist/cli/bin/index.js", "require": "./dist/cli/bin/index.cjs"}, "./preview/runtime": {"import": "./dist/preview/runtime.js"}, "./manager/globals-runtime": {"import": "./dist/manager/globals-runtime.js"}, "./package.json": "./package.json"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/index.d.ts"], "node-logger": ["./dist/node-logger/index.d.ts"], "client-logger": ["./dist/client-logger/index.d.ts"], "theming": ["./dist/theming/index.d.ts"], "theming/create": ["./dist/theming/create.d.ts"], "core-server": ["./dist/core-server/index.d.ts"], "core-events": ["./dist/core-events/index.d.ts"], "manager-errors": ["./dist/manager-errors.d.ts"], "preview-errors": ["./dist/preview-errors.d.ts"], "server-errors": ["./dist/server-errors.d.ts"], "channels": ["./dist/channels/index.d.ts"], "types": ["./dist/types/index.d.ts"], "csf-tools": ["./dist/csf-tools/index.d.ts"], "csf": ["./dist/csf/index.d.ts"], "common": ["./dist/common/index.d.ts"], "builder-manager": ["./dist/builder-manager/index.d.ts"], "telemetry": ["./dist/telemetry/index.d.ts"], "preview-api": ["./dist/preview-api/index.d.ts"], "manager-api": ["./dist/manager-api/index.d.ts"], "router": ["./dist/router/index.d.ts"], "components": ["./dist/components/index.d.ts"], "docs-tools": ["./dist/docs-tools/index.d.ts"], "manager/globals-module-info": ["./dist/manager/globals-module-info.d.ts"], "manager/globals": ["./dist/manager/globals.d.ts"], "preview/globals": ["./dist/preview/globals.d.ts"], "cli": ["./dist/cli/index.d.ts"], "babel": ["./dist/babel/index.d.ts"], "cli/bin": ["./dist/cli/bin/index.d.ts"]}}, "files": ["dist/**/*", "assets/**/*", "README.md", "!src/**/*"], "scripts": {"check": "jiti ./scripts/check.ts", "prep": "jiti ./scripts/prep.ts"}, "dependencies": {"@storybook/theming": "8.6.14", "better-opn": "^3.0.2", "browser-assert": "^1.2.1", "esbuild": "^0.18.0 || ^0.19.0 || ^0.20.0 || ^0.21.0 || ^0.22.0 || ^0.23.0 || ^0.24.0 || ^0.25.0", "esbuild-register": "^3.5.0", "jsdoc-type-pratt-parser": "^4.0.0", "process": "^0.11.10", "recast": "^0.23.5", "semver": "^7.6.2", "util": "^0.12.5", "ws": "^8.2.3"}, "devDependencies": {"@aw-web-design/x-default-browser": "1.4.126", "@babel/core": "^7.24.4", "@babel/generator": "^7.24.4", "@babel/parser": "^7.24.4", "@babel/preset-react": "^7.24.1", "@babel/traverse": "^7.24.1", "@babel/types": "^7.24.0", "@discoveryjs/json-ext": "^0.5.3", "@emotion/cache": "^11.14.0", "@emotion/is-prop-valid": "^1.3.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@emotion/use-insertion-effect-with-fallbacks": "^1.2.0", "@fal-works/esbuild-plugin-global-externals": "^2.1.2", "@ndelangen/get-tarball": "^3.0.7", "@ngard/tiny-isequal": "^1.1.0", "@polka/compression": "^1.0.0-next.28", "@popperjs/core": "^2.6.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-scroll-area": "1.2.0-rc.7", "@radix-ui/react-slot": "^1.0.2", "@storybook/docs-mdx": "4.0.0-next.1", "@storybook/global": "^5.0.0", "@storybook/icons": "^1.2.12", "@tanstack/react-virtual": "^3.3.0", "@testing-library/react": "^14.0.0", "@types/cross-spawn": "^6.0.2", "@types/detect-port": "^1.3.0", "@types/diff": "^5.0.9", "@types/ejs": "^3.1.1", "@types/find-cache-dir": "^5.0.0", "@types/js-yaml": "^4.0.5", "@types/node": "^22.0.0", "@types/npmlog": "^7.0.0", "@types/picomatch": "^2.3.0", "@types/prettier": "^3.0.0", "@types/pretty-hrtime": "^1.0.0", "@types/prompts": "^2.0.9", "@types/react-syntax-highlighter": "11.0.5", "@types/react-transition-group": "^4", "@types/semver": "^7.5.8", "@types/ws": "^8", "@yarnpkg/esbuild-plugin-pnp": "^3.0.0-rc.10", "@yarnpkg/fslib": "2.10.3", "@yarnpkg/libzip": "2.3.0", "ansi-to-html": "^0.7.2", "assert": "^2.1.0", "babel-plugin-react-docgen": "4.2.1", "boxen": "^7.1.1", "browser-dtector": "^3.4.0", "camelcase": "^8.0.0", "cli-table3": "^0.6.1", "commander": "^12.1.0", "comment-parser": "^1.4.1", "copy-to-clipboard": "^3.3.1", "cross-spawn": "^7.0.3", "css": "^3.0.0", "deep-object-diff": "^1.1.0", "dequal": "^2.0.2", "detect-indent": "^7.0.1", "detect-port": "^1.3.0", "diff": "^5.2.0", "downshift": "^9.0.4", "ejs": "^3.1.10", "es-toolkit": "^1.22.0", "esbuild": "^0.18.0 || ^0.19.0 || ^0.20.0 || ^0.21.0 || ^0.22.0 || ^0.23.0 || ^0.24.0 || ^0.25.0", "esbuild-plugin-alias": "^0.2.1", "execa": "^8.0.1", "fd-package-json": "^1.2.0", "fetch-retry": "^6.0.0", "find-cache-dir": "^5.0.0", "find-up": "^7.0.0", "flush-promises": "^1.0.2", "fuse.js": "^3.6.1", "get-npm-tarball-url": "^2.0.3", "glob": "^10.0.0", "globby": "^14.0.1", "jiti": "^1.21.6", "js-yaml": "^4.1.0", "lazy-universal-dotenv": "^4.0.0", "leven": "^4.0.0", "markdown-to-jsx": "^7.7.2", "memfs": "^4.11.1", "memoizerific": "^1.11.3", "nanoid": "^4.0.2", "npmlog": "^7.0.0", "open": "^8.4.0", "package-manager-detector": "^1.1.0", "picocolors": "^1.1.0", "picomatch": "^2.3.0", "picoquery": "^1.4.0", "polished": "^4.2.2", "polka": "^1.0.0-next.28", "prettier": "^3.2.5", "pretty-hrtime": "^1.0.3", "prompts": "^2.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-helmet-async": "^1.3.0", "react-popper-tooltip": "^4.4.2", "react-resize-detector": "^7.1.2", "react-router-dom": "6.0.2", "react-syntax-highlighter": "^15.4.5", "react-textarea-autosize": "^8.3.0", "react-transition-group": "^4.4.5", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0", "sirv": "^2.0.4", "slash": "^5.0.0", "source-map": "^0.7.4", "store2": "^2.14.2", "strip-json-comments": "^5.0.1", "telejson": "^7.2.0", "tiny-invariant": "^1.3.1", "tinyspy": "^2.2.0", "ts-dedent": "^2.0.0", "tsconfig-paths": "^4.2.0", "type-fest": "^4.18.1", "typescript": "^5.7.3", "unique-string": "^3.0.0", "use-resize-observer": "^9.1.0", "watchpack": "^2.2.0", "zod": "^3.24.1"}, "peerDependencies": {"prettier": "^2 || ^3"}, "peerDependenciesMeta": {"prettier": {"optional": true}}, "publishConfig": {"access": "public"}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae17"}