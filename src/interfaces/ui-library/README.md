# @cohere-ai/ui-library

Shared UI component library for Cohere Toolkit frontends (assistants_web and coral_web).

## Features

- 🎨 **TailwindCSS** with unified design tokens
- 🧩 **ShadcnUI** components with full customization
- 📚 **Storybook** integration for documentation
- 🔒 **TypeScript** support with comprehensive types
- 🌙 **Dark/Light** theme support
- ♿ **Accessibility** WCAG 2.1 AA compliant
- 📦 **Tree-shakable** exports for optimal bundle sizes

## Installation

```bash
npm install @cohere-ai/ui-library
```

## Usage

```tsx
import { Button, Input, Text } from '@cohere-ai/ui-library';
import '@cohere-ai/ui-library/dist/styles.css';

function App() {
  return (
    <div>
      <Text as="h1" styleAs="h1">Welcome to Cohere Toolkit</Text>
      <Input label="Email" type="email" />
      <Button kind="primary" theme="evolved-green">
        Get Started
      </Button>
    </div>
  );
}
```

## Development

```bash
# Install dependencies
npm install

# Start development mode
npm run dev

# Run Storybook
npm run storybook

# Run tests
npm run test

# Build library
npm run build
```

## Components

### Core Components
- Button - Primary interaction element with multiple variants
- Input - Form input with validation and accessibility features
- Text - Typography system with responsive sizing
- Icon - Unified icon system with accessibility support

### Form Components
- Checkbox - Themed checkbox with proper form integration
- RadioGroup - Radio button groups with keyboard navigation
- Switch - Toggle switch with animation support

### Layout Components
- Modal - Accessible modal dialogs with focus management
- Dropdown - Dropdown menus with keyboard navigation
- Tabs - Tab navigation with proper ARIA support

### Feedback Components
- Spinner - Loading indicators with size variants
- Tooltip - Contextual help with proper positioning
- Toast - Notification system with queue management

## Theme System

The library supports both dark and light themes with CSS variables:

```tsx
import { ThemeProvider } from '@cohere-ai/ui-library';

function App() {
  return (
    <ThemeProvider defaultTheme="dark" enableSystem>
      {/* Your app content */}
    </ThemeProvider>
  );
}
```

## Contributing

Please refer to the main Cohere Toolkit repository for contribution guidelines.

## License

MIT
