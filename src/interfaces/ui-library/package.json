{"name": "@cohere-ai/ui-library", "version": "0.1.0", "description": "Shared UI component library for Cohere Toolkit frontends", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "engines": {"npm": ">=9", "node": ">=20.12.2", "pnpm": "9999", "yarn": "9999"}, "scripts": {"dev": "vite build --watch", "build": "vite build && tsc --emitDeclarationOnly", "build:watch": "vite build --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6007", "build-storybook": "storybook build", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.400.0", "next-themes": "^0.4.6", "react-day-picker": "^9.8.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "sonner": "^2.0.6", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.74"}, "devDependencies": {"@chromatic-com/storybook": "^1.6.1", "@storybook/addon-essentials": "^8.2.6", "@storybook/addon-interactions": "^8.2.6", "@storybook/addon-links": "^8.2.6", "@storybook/blocks": "^8.2.6", "@storybook/react": "^8.2.6", "@storybook/react-vite": "^8.2.6", "@storybook/test": "^8.2.6", "@tailwindcss/typography": "^0.5.9", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^2.0.2", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-storybook": "^0.8.0", "jsdom": "^24.1.0", "postcss": "^8.4.39", "prettier": "^3.3.2", "rimraf": "^5.0.7", "storybook": "^8.2.6", "tailwindcss": "^3.4.4", "typescript": "^5.5.3", "vite": "^5.3.3", "vite-plugin-dts": "^3.9.1", "vitest": "^2.0.2"}, "keywords": ["react", "components", "ui", "design-system", "tailwindcss", "typescript", "cohere"], "author": "Cohere AI", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cohere-ai/cohere-toolkit.git", "directory": "src/interfaces/ui-library"}}