import { Config } from 'tailwindcss';
import defaultTheme from 'tailwindcss/defaultTheme';
import typography from '@tailwindcss/typography';
import animate from 'tailwindcss-animate';

const config: Config = {
  content: ['src/**/*.{js,jsx,ts,tsx}'],
  darkMode: ['class', 'class'],
  plugins: [typography, animate],
  safelist: [
    {
      pattern:
        /(bg|text|border|fill)-(blue|evolved-blue|coral|green|evolved-green|quartz|evolved-quartz|mushroom|evolved-mushroom|marble|volcanic|danger)-\S+/,
      variants: ['hover', 'dark', 'dark:hover'],
    },
  ],
  theme: {
  	extend: {
  		colors: {
  			black: '#212121',
  			white: '#FAFAFA',
  			coral: {
  				'150': '#330900',
  				'200': '#661300',
  				'300': '#991C00',
  				'400': '#CC2500',
  				'500': '#FF2F00',
  				'600': '#FF5833',
  				'700': '#FF8266',
  				'800': '#FFAC99',
  				'900': '#FFD5CC',
  				'950': '#FFEAE5'
  			},
  			mushroom: {
  				'150': '#2C2821',
  				'200': '#3A352C',
  				'300': '#575042',
  				'400': '#70695C',
  				'500': '#91856E',
  				'600': '#A79E8B',
  				'700': '#BDB6A8',
  				'800': '#D2CDC4',
  				'900': '#E9E7E2',
  				'950': '#F4F3F0'
  			},
  			'evolved-mushroom': {
  				'500': '#FFAA00',
  				'600': '#FFBB33',
  				'800': '#FFDC97'
  			},
  			marble: {
  				'800': '#C4C4C4',
  				'850': '#D0D0E2',
  				'900': '#DFDFEC',
  				'950': '#EFEFF5',
  				'980': '#F9F9FB',
  				'1000': '#FFFFFF'
  			},
  			volcanic: {
  				'60': '#0D0D15',
  				'100': '#13131F',
  				'150': '#1F1F2E',
  				'200': '#2C2C3A',
  				'300': '#39394A',
  				'400': '#62626C',
  				'500': '#7C7C8A',
  				'600': '#9E9EAF',
  				'700': '#C5C5D2',
  				'800': '#DCDCE3',
  				'900': '#EDEDF0',
  				'950': '#F7F7F8'
  			},
  			blue: {
  				'150': '#003D99',
  				'200': '#0052CC',
  				'300': '#0066F5',
  				'400': '#0F70FF',
  				'500': '#3388FF',
  				'600': '#5CA0FF',
  				'700': '#85B8FF',
  				'800': '#ADCFFF',
  				'900': '#D6E7FF',
  				'950': '#EBF3FF'
  			},
  			'evolved-blue': {
  				'500': '#1B6AFF',
  				'600': '#4785FF',
  				'800': '#99BFFF'
  			},
  			green: {
  				'150': '#006B44',
  				'200': '#008A55',
  				'300': '#00A866',
  				'400': '#00C278',
  				'500': '#2ACC8F',
  				'600': '#54D6A5',
  				'700': '#7FE0BC',
  				'800': '#A9EBD2',
  				'900': '#D4F5E9',
  				'950': '#EAFAF4'
  			},
  			'evolved-green': {
  				'500': '#00B958',
  				'600': '#33C973',
  				'800': '#99E4B8'
  			},
  			quartz: {
  				'150': '#936F35',
  				'200': '#9F7F4B',
  				'300': '#AB8F62',
  				'400': '#B79F78',
  				'500': '#C3AF8F',
  				'600': '#CFBFA5',
  				'700': '#DBCFBC',
  				'800': '#E7DFD2',
  				'900': '#F3EFE9',
  				'950': '#F9F7F4'
  			},
  			'evolved-quartz': {
  				'500': '#D4AF37',
  				'600': '#DCC255',
  				'800': '#EDDB99'
  			},
  			success: {
  				'150': '#006B44',
  				'200': '#008A55',
  				'300': '#00A866',
  				'400': '#00C278',
  				'500': '#2ACC8F',
  				'600': '#54D6A5',
  				'700': '#7FE0BC',
  				'800': '#A9EBD2',
  				'900': '#D4F5E9',
  				'950': '#EAFAF4'
  			},
  			danger: {
  				'150': '#FF4747',
  				'200': '#FF5C5C',
  				'300': '#FF7070',
  				'400': '#FF8585',
  				'500': '#FF9999',
  				'600': '#FFADAD',
  				'700': '#FFC2C2',
  				'800': '#FFD6D6',
  				'900': '#FFEBEB',
  				'950': '#FFF5F5'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			}
  		},
  		fontFamily: {
  			sans: [
  				'ui-sans-serif',
  				'system-ui',
  				'-apple-system',
  				'BlinkMacSystemFont',
  				'Segoe UI',
  				'Roboto',
  				'Helvetica Neue',
  				'Arial',
  				'Noto Sans',
  				'sans-serif',
  				'Apple Color Emoji',
  				'Segoe UI Emoji',
  				'Segoe UI Symbol',
  				'Noto Color Emoji'
  			],
  			body: [
  				'CohereText',
  				'Arial',
                    ...defaultTheme.fontFamily.sans
                ],
  			variable: [
  				'CohereVariable',
  				'Arial',
                    ...defaultTheme.fontFamily.serif
                ],
  			code: [
  				'CohereMono',
                    ...defaultTheme.fontFamily.mono
                ]
  		},
  		fontWeight: {
  			medium: '525'
  		},
  		spacing: {
  			'icon-xs': '12px',
  			'icon-sm': '14px',
  			'icon-md': '16px',
  			'icon-lg': '24px',
  			'icon-xl': '36px',
  			'cell-xs': '24px',
  			'cell-sm': '32px',
  			'cell-md': '40px',
  			'cell-lg': '50px',
  			'cell-xl': '64px',
  			'cell-button': '40px'
  		},
  		width: {
  			'icon-xs': '12px',
  			'icon-sm': '14px',
  			'icon-md': '16px',
  			'icon-lg': '24px',
  			'icon-xl': '36px',
  			'btn-sm': '280px',
  			'btn-md': '312px',
  			'btn-lg': '350px',
  			'btn-xl': '370px',
  			modal: '648px',
  			toast: '320px',
  			'toast-sm': '280px',
  			'citation-md': '250px',
  			'citation-lg': '298px',
  			'citation-xl': '320px',
  			file: '224px',
  			'edit-agent-panel': '350px',
  			'edit-agent-panel-lg': '683px',
  			'edit-agent-panel-2xl': '800px'
  		},
  		minWidth: {
  			menu: '174px',
  			'left-panel-collapsed': '82px',
  			'left-panel-expanded': '288px',
  			'citation-panel-md': '259px',
  			'citation-panel-lg': '325px',
  			'citation-panel-xl': '349px',
  			'agents-panel-collapsed': '82px',
  			'agents-panel-expanded': '288px',
  			'agents-panel-expanded-lg': '320px',
  			'left-panel-lg': '242px',
  			'left-panel-2xl': '300px',
  			'left-panel-3xl': '360px'
  		},
  		height: {
  			'cell-button': '40px'
  		},
  		maxWidth: {
  			message: '976px',
  			'share-content': '700px',
  			'left-panel-collapsed': '82px',
  			'left-panel-expanded': '288px'
  		},
  		maxHeight: {
  			'cell-xs': '24px',
  			'cell-sm': '32px',
  			'cell-md': '40px',
  			'cell-lg': '50px',
  			'cell-xl': '64px'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		zIndex: {
  			'main-section': '10',
  			'tag-suggestions': '10',
  			'drag-drop-input-overlay': '10',
  			'configuration-drawer': '20',
  			'selected-citation': '20',
  			backdrop: '20',
  			navigation: '30',
  			'read-only-conversation-footer': '30',
  			'guide-tooltip': '30',
  			modal: '50',
  			tooltip: '50',
  			dropdown: '60',
  			toast: '70',
  			menu: '90'
  		},
  		boxShadow: {
  			drawer: '-10px 4px 12px -10px rgba(197, 188, 172, 0.48)',
  			menu: '0px 4px 12px 0px rgba(197, 188, 172, 0.48)',
  			top: '4px 0px 12px 0px rgba(197, 188, 172, 0.48)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		}
  	}
  },
};

export default config;
